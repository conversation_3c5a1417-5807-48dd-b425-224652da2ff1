using Microsoft.EntityFrameworkCore;
using KelimeZinciri.API.Data;
using KelimeZinciri.API.Models;

namespace KelimeZinciri.API.Services
{
    public interface IGameService
    {
        Task<GameDto> CreateGameAsync(int userId, CreateGameDto createGameDto);
        Task<GameDto?> JoinGameAsync(int userId, string roomCode);
        Task<GameDto?> GetGameAsync(int gameId);
        Task<GameDto?> GetGameByRoomCodeAsync(string roomCode);
        Task<bool> StartGameAsync(int gameId, int userId);
        Task<GameMoveResult> SubmitWordAsync(int gameId, int userId, string word);
        Task<bool> LeaveGameAsync(int gameId, int userId);
        Task<List<GameDto>> GetActiveGamesAsync();
        Task<List<GameDto>> GetUserGamesAsync(int userId, int page = 1, int pageSize = 10);
        string GenerateRoomCode();
    }

    public class GameService : IGameService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWordService _wordService;
        private readonly ILogger<GameService> _logger;

        public GameService(ApplicationDbContext context, IWordService wordService, ILogger<GameService> logger)
        {
            _context = context;
            _wordService = wordService;
            _logger = logger;
        }

        public async Task<GameDto> CreateGameAsync(int userId, CreateGameDto createGameDto)
        {
            try
            {
                var roomCode = GenerateRoomCode();

                var game = new Game
                {
                    RoomCode = roomCode,
                    CreatedByUserId = userId,
                    Difficulty = createGameDto.Difficulty,
                    MaxPlayers = createGameDto.MaxPlayers,
                    TimeLimit = createGameDto.TimeLimit,
                    MaxRounds = createGameDto.MaxRounds,
                    Status = GameStatus.Waiting,
                    CreatedAt = DateTime.UtcNow,
                    CurrentPlayerCount = 1
                };

                _context.Games.Add(game);
                await _context.SaveChangesAsync();

                // Add creator as first player
                var gamePlayer = new GamePlayer
                {
                    GameId = game.Id,
                    UserId = userId,
                    Position = 1,
                    IsActive = true,
                    JoinedAt = DateTime.UtcNow
                };

                _context.GamePlayers.Add(gamePlayer);
                await _context.SaveChangesAsync();

                return await MapToGameDto(game);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating game for user: {UserId}", userId);
                throw;
            }
        }

        public async Task<GameDto?> JoinGameAsync(int userId, string roomCode)
        {
            try
            {
                var game = await _context.Games
                    .Include(g => g.Players)
                    .ThenInclude(p => p.User)
                    .FirstOrDefaultAsync(g => g.RoomCode == roomCode && g.Status == GameStatus.Waiting);

                if (game == null)
                {
                    return null; // Game not found or not waiting
                }

                if (game.CurrentPlayerCount >= game.MaxPlayers)
                {
                    return null; // Game is full
                }

                // Check if user already in game
                var existingPlayer = game.GamePlayers.FirstOrDefault(p => p.UserId == userId);
                if (existingPlayer != null)
                {
                    return await MapToGameDto(game); // Already in game
                }

                var gamePlayer = new GamePlayer
                {
                    GameId = game.Id,
                    UserId = userId,
                    Position = game.CurrentPlayerCount + 1,
                    IsActive = true,
                    JoinedAt = DateTime.UtcNow
                };

                _context.GamePlayers.Add(gamePlayer);

                game.CurrentPlayerCount++;
                await _context.SaveChangesAsync();

                return await MapToGameDto(game);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error joining game {RoomCode} for user: {UserId}", roomCode, userId);
                return null;
            }
        }

        public async Task<GameDto?> GetGameAsync(int gameId)
        {
            try
            {
                var game = await _context.Games
                    .Include(g => g.CreatedByUser)
                    .Include(g => g.GamePlayers)
                    .ThenInclude(p => p.User)
                    .Include(g => g.Moves)
                    .ThenInclude(m => m.User)
                    .FirstOrDefaultAsync(g => g.Id == gameId);

                return game != null ? await MapToGameDto(game) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting game: {GameId}", gameId);
                return null;
            }
        }

        public async Task<GameDto?> GetGameByRoomCodeAsync(string roomCode)
        {
            try
            {
                var game = await _context.Games
                    .Include(g => g.CreatedByUser)
                    .Include(g => g.GamePlayers)
                    .ThenInclude(p => p.User)
                    .Include(g => g.Moves)
                    .ThenInclude(m => m.User)
                    .FirstOrDefaultAsync(g => g.RoomCode == roomCode);

                return game != null ? await MapToGameDto(game) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting game by room code: {RoomCode}", roomCode);
                return null;
            }
        }

        public async Task<bool> StartGameAsync(int gameId, int userId)
        {
            try
            {
                var game = await _context.Games
                    .Include(g => g.GamePlayers)
                    .FirstOrDefaultAsync(g => g.Id == gameId);

                if (game == null || game.CreatedByUserId != userId || game.Status != GameStatus.Waiting)
                {
                    return false;
                }

                if (game.CurrentPlayerCount < 2)
                {
                    return false; // Need at least 2 players
                }

                game.Status = GameStatus.InProgress;
                game.StartedAt = DateTime.UtcNow;
                game.CurrentRound = 1;

                // Set first player turn (random)
                var players = game.GamePlayers.Where(p => p.IsActive).ToList();
                var randomPlayer = players[new Random().Next(players.Count)];
                game.CurrentPlayerTurnId = randomPlayer.UserId;

                // Generate starting word
                var startingWord = await _wordService.GetRandomWordByLetterAsync('a',
                    game.Difficulty == GameDifficulty.Easy ? WordDifficulty.Easy :
                    game.Difficulty == GameDifficulty.Medium ? WordDifficulty.Medium : WordDifficulty.Hard);

                if (startingWord != null)
                {
                    game.CurrentWord = startingWord.Text;
                    game.RequiredStartLetter = startingWord.LastLetter;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting game: {GameId}", gameId);
                return false;
            }
        }

        public async Task<GameMoveResult> SubmitWordAsync(int gameId, int userId, string word)
        {
            try
            {
                var game = await _context.Games
                    .Include(g => g.GamePlayers)
                    .Include(g => g.Moves)
                    .FirstOrDefaultAsync(g => g.Id == gameId);

                if (game == null || game.Status != GameStatus.InProgress)
                {
                    return new GameMoveResult { Success = false, Message = "Oyun bulunamadı veya aktif değil." };
                }

                if (game.CurrentPlayerTurnId != userId)
                {
                    return new GameMoveResult { Success = false, Message = "Sıra sizde değil." };
                }

                // Validate word
                var validation = await _wordService.ValidateWordAsync(word, game.RequiredStartLetter);

                if (!validation.IsValid)
                {
                    return new GameMoveResult
                    {
                        Success = false,
                        Message = validation.Reason ?? "Geçersiz kelime.",
                        IsValidWord = false
                    };
                }

                // Check if word was already used in this game
                var wordAlreadyUsed = await _context.GameMoves
                    .AnyAsync(m => m.GameId == gameId && m.Word.ToLower() == word.ToLower());

                if (wordAlreadyUsed)
                {
                    return new GameMoveResult
                    {
                        Success = false,
                        Message = "Bu kelime bu oyunda zaten kullanıldı.",
                        IsValidWord = true
                    };
                }

                // Calculate points
                var responseTime = DateTime.UtcNow - (game.Moves.LastOrDefault()?.CreatedAt ?? game.StartedAt ?? DateTime.UtcNow);
                var points = await _wordService.CalculateWordPointsAsync(word, responseTime, game.Difficulty);

                // Create game move
                var gameMove = new GameMove
                {
                    GameId = gameId,
                    UserId = userId,
                    Word = word.ToLowerInvariant(),
                    Round = game.CurrentRound,
                    Points = points,
                    IsValid = true,
                    CreatedAt = DateTime.UtcNow,
                    ResponseTime = responseTime
                };

                _context.GameMoves.Add(gameMove);

                // Update player score
                var player = game.GamePlayers.First(p => p.UserId == userId);
                player.Score += points;

                // Update game state
                game.CurrentWord = word.ToLowerInvariant();
                game.RequiredStartLetter = word.ToLowerInvariant()[word.Length - 1];

                // Move to next player
                var activePlayers = game.GamePlayers.Where(p => p.IsActive).OrderBy(p => p.Position).ToList();
                var currentPlayerIndex = activePlayers.FindIndex(p => p.UserId == userId);
                var nextPlayerIndex = (currentPlayerIndex + 1) % activePlayers.Count;
                game.CurrentPlayerTurnId = activePlayers[nextPlayerIndex].UserId;

                // Check if round should end
                if (game.Moves.Count(m => m.Round == game.CurrentRound) >= game.MaxPlayers)
                {
                    game.CurrentRound++;

                    // Check if game should end
                    if (game.CurrentRound > game.MaxRounds)
                    {
                        game.Status = GameStatus.Finished;
                        game.EndedAt = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();

                return new GameMoveResult
                {
                    Success = true,
                    Message = "Kelime kabul edildi!",
                    IsValidWord = true,
                    Points = points,
                    NextPlayerTurnId = game.CurrentPlayerTurnId,
                    RequiredStartLetter = game.RequiredStartLetter,
                    GameStatus = game.Status.ToString()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting word {Word} for game {GameId} by user {UserId}", word, gameId, userId);
                return new GameMoveResult { Success = false, Message = "Kelime gönderilirken hata oluştu." };
            }
        }

        public async Task<bool> LeaveGameAsync(int gameId, int userId)
        {
            try
            {
                var gamePlayer = await _context.GamePlayers
                    .Include(gp => gp.Game)
                    .FirstOrDefaultAsync(gp => gp.GameId == gameId && gp.UserId == userId);

                if (gamePlayer == null) return false;

                gamePlayer.IsActive = false;
                gamePlayer.Game.CurrentPlayerCount--;

                // If game creator leaves and game hasn't started, cancel the game
                if (gamePlayer.Game.CreatedByUserId == userId && gamePlayer.Game.Status == GameStatus.Waiting)
                {
                    gamePlayer.Game.Status = GameStatus.Cancelled;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving game {GameId} for user {UserId}", gameId, userId);
                return false;
            }
        }

        public async Task<List<GameDto>> GetActiveGamesAsync()
        {
            try
            {
                var games = await _context.Games
                    .Include(g => g.CreatedByUser)
                    .Include(g => g.GamePlayers)
                    .ThenInclude(p => p.User)
                    .Where(g => g.Status == GameStatus.Waiting && g.CurrentPlayerCount < g.MaxPlayers)
                    .OrderByDescending(g => g.CreatedAt)
                    .Take(20)
                    .ToListAsync();

                var gameDtos = new List<GameDto>();
                foreach (var game in games)
                {
                    gameDtos.Add(await MapToGameDto(game));
                }

                return gameDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active games");
                return new List<GameDto>();
            }
        }

        public async Task<List<GameDto>> GetUserGamesAsync(int userId, int page = 1, int pageSize = 10)
        {
            try
            {
                var games = await _context.Games
                    .Include(g => g.CreatedByUser)
                    .Include(g => g.GamePlayers)
                    .ThenInclude(p => p.User)
                    .Where(g => g.GamePlayers.Any(p => p.UserId == userId))
                    .OrderByDescending(g => g.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var gameDtos = new List<GameDto>();
                foreach (var game in games)
                {
                    gameDtos.Add(await MapToGameDto(game));
                }

                return gameDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user games for user: {UserId}", userId);
                return new List<GameDto>();
            }
        }

        public string GenerateRoomCode()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 6)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        private async Task<GameDto> MapToGameDto(Game game)
        {
            // Load related data if not already loaded
            if (game.CreatedByUser == null || game.GamePlayers == null)
            {
                game = await _context.Games
                    .Include(g => g.CreatedByUser)
                    .Include(g => g.GamePlayers)
                    .ThenInclude(p => p.User)
                    .Include(g => g.Moves)
                    .ThenInclude(m => m.User)
                    .FirstAsync(g => g.Id == game.Id);
            }

            return new GameDto
            {
                Id = game.Id,
                RoomCode = game.RoomCode,
                CreatedByUser = new UserDto
                {
                    Id = game.CreatedByUser.Id,
                    Username = game.CreatedByUser.Username,
                    Avatar = game.CreatedByUser.Avatar
                },
                Difficulty = game.Difficulty.ToString(),
                Status = game.Status.ToString(),
                MaxPlayers = game.MaxPlayers,
                CurrentPlayerCount = game.CurrentPlayerCount,
                TimeLimit = game.TimeLimit,
                MaxRounds = game.MaxRounds,
                CurrentRound = game.CurrentRound,
                CurrentWord = game.CurrentWord,
                RequiredStartLetter = game.RequiredStartLetter,
                CurrentPlayerTurnId = game.CurrentPlayerTurnId,
                CreatedAt = game.CreatedAt,
                StartedAt = game.StartedAt,
                EndedAt = game.EndedAt,
                Players = game.GamePlayers.Where(p => p.IsActive).Select(p => new GamePlayerDto
                {
                    Id = p.Id,
                    User = new UserDto
                    {
                        Id = p.User.Id,
                        Username = p.User.Username,
                        Avatar = p.User.Avatar
                    },
                    Score = p.Score,
                    Position = p.Position,
                    JoinedAt = p.JoinedAt
                }).ToList(),
                RecentMoves = game.Moves?.OrderByDescending(m => m.CreatedAt).Take(10).Select(m => new GameMoveDto
                {
                    Id = m.Id,
                    User = new UserDto
                    {
                        Id = m.User.Id,
                        Username = m.User.Username,
                        Avatar = m.User.Avatar
                    },
                    Word = m.Word,
                    Points = m.Points,
                    Round = m.Round,
                    CreatedAt = m.CreatedAt,
                    ResponseTime = m.ResponseTime
                }).ToList() ?? new List<GameMoveDto>()
            };
        }
    }

    // DTOs
    public class CreateGameDto
    {
        public GameDifficulty Difficulty { get; set; } = GameDifficulty.Easy;
        public int MaxPlayers { get; set; } = 4;
        public int TimeLimit { get; set; } = 30;
        public int MaxRounds { get; set; } = 10;
    }

    public class GameDto
    {
        public int Id { get; set; }
        public string RoomCode { get; set; } = string.Empty;
        public UserDto CreatedByUser { get; set; } = null!;
        public string Difficulty { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int MaxPlayers { get; set; }
        public int CurrentPlayerCount { get; set; }
        public int TimeLimit { get; set; }
        public int MaxRounds { get; set; }
        public int CurrentRound { get; set; }
        public string? CurrentWord { get; set; }
        public char? RequiredStartLetter { get; set; }
        public int? CurrentPlayerTurnId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? EndedAt { get; set; }
        public List<GamePlayerDto> Players { get; set; } = new();
        public List<GameMoveDto> RecentMoves { get; set; } = new();
    }

    public class GamePlayerDto
    {
        public int Id { get; set; }
        public UserDto User { get; set; } = null!;
        public int Score { get; set; }
        public int Position { get; set; }
        public DateTime JoinedAt { get; set; }
    }

    public class GameMoveDto
    {
        public int Id { get; set; }
        public UserDto User { get; set; } = null!;
        public string Word { get; set; } = string.Empty;
        public int Points { get; set; }
        public int Round { get; set; }
        public DateTime CreatedAt { get; set; }
        public TimeSpan ResponseTime { get; set; }
    }

    public class GameMoveResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public bool IsValidWord { get; set; }
        public int Points { get; set; }
        public int? NextPlayerTurnId { get; set; }
        public char? RequiredStartLetter { get; set; }
        public string? GameStatus { get; set; }
    }
}
