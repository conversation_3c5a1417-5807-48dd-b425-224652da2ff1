using Microsoft.EntityFrameworkCore;
using KelimeZinciri.API.Data;
using KelimeZinciri.API.Models;

namespace KelimeZinciri.API.Services
{
    public interface IUserService
    {
        Task<UserDto?> UpdateProfileAsync(int userId, UpdateProfileDto updateProfileDto);
        Task<string?> UploadAvatarAsync(int userId, IFormFile file);
        Task<List<UserSearchResult>> SearchUsersAsync(string query, int limit = 20);
        Task<bool> SendFriendRequestAsync(int fromUserId, int toUserId);
        Task<bool> AcceptFriendRequestAsync(int userId, int friendRequestId);
        Task<bool> RejectFriendRequestAsync(int userId, int friendRequestId);
        Task<List<FriendRequest>> GetFriendRequestsAsync(int userId);
        Task<List<UserDto>> GetFriendsAsync(int userId);
        Task<bool> RemoveFriendAsync(int userId, int friendId);
    }

    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UserService> _logger;
        private readonly IWebHostEnvironment _environment;

        public UserService(ApplicationDbContext context, ILogger<UserService> logger, IWebHostEnvironment environment)
        {
            _context = context;
            _logger = logger;
            _environment = environment;
        }

        public async Task<UserDto?> UpdateProfileAsync(int userId, UpdateProfileDto updateProfileDto)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return null;
                }

                // Check if username is already taken by another user
                if (!string.IsNullOrEmpty(updateProfileDto.Username) && 
                    updateProfileDto.Username != user.Username)
                {
                    var existingUser = await _context.Users
                        .FirstOrDefaultAsync(u => u.Username == updateProfileDto.Username && u.Id != userId);
                    
                    if (existingUser != null)
                    {
                        throw new ArgumentException("Bu kullanıcı adı zaten kullanılıyor.");
                    }
                    
                    user.Username = updateProfileDto.Username;
                }

                // Check if email is already taken by another user
                if (!string.IsNullOrEmpty(updateProfileDto.Email) && 
                    updateProfileDto.Email != user.Email)
                {
                    var existingUser = await _context.Users
                        .FirstOrDefaultAsync(u => u.Email == updateProfileDto.Email && u.Id != userId);
                    
                    if (existingUser != null)
                    {
                        throw new ArgumentException("Bu e-posta adresi zaten kullanılıyor.");
                    }
                    
                    user.Email = updateProfileDto.Email;
                }

                if (!string.IsNullOrEmpty(updateProfileDto.FirstName))
                {
                    user.FirstName = updateProfileDto.FirstName;
                }

                if (!string.IsNullOrEmpty(updateProfileDto.LastName))
                {
                    user.LastName = updateProfileDto.LastName;
                }

                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Avatar = user.Avatar,
                    CreatedAt = user.CreatedAt,
                    LastLoginAt = user.LastLoginAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile for user {UserId}", userId);
                throw;
            }
        }

        public async Task<string?> UploadAvatarAsync(int userId, IFormFile file)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return null;
                }

                // Validate file
                if (file == null || file.Length == 0)
                {
                    throw new ArgumentException("Dosya seçilmedi.");
                }

                // Check file size (max 5MB)
                if (file.Length > 5 * 1024 * 1024)
                {
                    throw new ArgumentException("Dosya boyutu 5MB'dan büyük olamaz.");
                }

                // Check file type
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    throw new ArgumentException("Sadece JPEG, PNG ve GIF dosyaları kabul edilir.");
                }

                // Create uploads directory if it doesn't exist
                var uploadsDir = Path.Combine(_environment.WebRootPath ?? "", "uploads", "avatars");
                if (!Directory.Exists(uploadsDir))
                {
                    Directory.CreateDirectory(uploadsDir);
                }

                // Generate unique filename
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = $"{userId}_{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsDir, fileName);

                // Delete old avatar if exists
                if (!string.IsNullOrEmpty(user.Avatar))
                {
                    var oldAvatarPath = Path.Combine(_environment.WebRootPath ?? "", user.Avatar.TrimStart('/'));
                    if (File.Exists(oldAvatarPath))
                    {
                        File.Delete(oldAvatarPath);
                    }
                }

                // Save new file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Update user avatar path
                var avatarUrl = $"/uploads/avatars/{fileName}";
                user.Avatar = avatarUrl;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return avatarUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading avatar for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<UserSearchResult>> SearchUsersAsync(string query, int limit = 20)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query) || query.Length < 2)
                {
                    return new List<UserSearchResult>();
                }

                var users = await _context.Users
                    .Where(u => u.Username.Contains(query) || 
                               (u.FirstName != null && u.FirstName.Contains(query)) ||
                               (u.LastName != null && u.LastName.Contains(query)))
                    .Take(limit)
                    .Select(u => new UserSearchResult
                    {
                        Id = u.Id,
                        Username = u.Username,
                        FirstName = u.FirstName,
                        LastName = u.LastName,
                        Avatar = u.Avatar
                    })
                    .ToListAsync();

                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching users with query: {Query}", query);
                throw;
            }
        }

        public async Task<bool> SendFriendRequestAsync(int fromUserId, int toUserId)
        {
            try
            {
                if (fromUserId == toUserId)
                {
                    throw new ArgumentException("Kendinize arkadaşlık isteği gönderemezsiniz.");
                }

                // Check if users exist
                var fromUser = await _context.Users.FindAsync(fromUserId);
                var toUser = await _context.Users.FindAsync(toUserId);

                if (fromUser == null || toUser == null)
                {
                    throw new ArgumentException("Kullanıcı bulunamadı.");
                }

                // Check if friend request already exists
                var existingRequest = await _context.FriendRequests
                    .FirstOrDefaultAsync(fr => fr.FromUserId == fromUserId && fr.ToUserId == toUserId);

                if (existingRequest != null)
                {
                    throw new ArgumentException("Zaten bir arkadaşlık isteği gönderilmiş.");
                }

                // Check if they are already friends
                var existingFriendship = await _context.Friendships
                    .FirstOrDefaultAsync(f => (f.UserId == fromUserId && f.FriendId == toUserId) ||
                                            (f.UserId == toUserId && f.FriendId == fromUserId));

                if (existingFriendship != null)
                {
                    throw new ArgumentException("Zaten arkadaşsınız.");
                }

                // Create friend request
                var friendRequest = new FriendRequest
                {
                    FromUserId = fromUserId,
                    ToUserId = toUserId,
                    Status = FriendRequestStatus.Pending,
                    CreatedAt = DateTime.UtcNow
                };

                _context.FriendRequests.Add(friendRequest);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending friend request from {FromUserId} to {ToUserId}", fromUserId, toUserId);
                throw;
            }
        }

        public async Task<bool> AcceptFriendRequestAsync(int userId, int friendRequestId)
        {
            try
            {
                var friendRequest = await _context.FriendRequests
                    .FirstOrDefaultAsync(fr => fr.Id == friendRequestId && fr.ToUserId == userId);

                if (friendRequest == null)
                {
                    throw new ArgumentException("Arkadaşlık isteği bulunamadı.");
                }

                if (friendRequest.Status != FriendRequestStatus.Pending)
                {
                    throw new ArgumentException("Bu istek zaten işlenmiş.");
                }

                // Update friend request status
                friendRequest.Status = FriendRequestStatus.Accepted;
                friendRequest.UpdatedAt = DateTime.UtcNow;

                // Create friendship (bidirectional)
                var friendship1 = new Friendship
                {
                    UserId = friendRequest.FromUserId,
                    FriendId = friendRequest.ToUserId,
                    CreatedAt = DateTime.UtcNow
                };

                var friendship2 = new Friendship
                {
                    UserId = friendRequest.ToUserId,
                    FriendId = friendRequest.FromUserId,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Friendships.AddRange(friendship1, friendship2);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting friend request {FriendRequestId} for user {UserId}", friendRequestId, userId);
                throw;
            }
        }

        public async Task<bool> RejectFriendRequestAsync(int userId, int friendRequestId)
        {
            try
            {
                var friendRequest = await _context.FriendRequests
                    .FirstOrDefaultAsync(fr => fr.Id == friendRequestId && fr.ToUserId == userId);

                if (friendRequest == null)
                {
                    throw new ArgumentException("Arkadaşlık isteği bulunamadı.");
                }

                if (friendRequest.Status != FriendRequestStatus.Pending)
                {
                    throw new ArgumentException("Bu istek zaten işlenmiş.");
                }

                friendRequest.Status = FriendRequestStatus.Rejected;
                friendRequest.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting friend request {FriendRequestId} for user {UserId}", friendRequestId, userId);
                throw;
            }
        }

        public async Task<List<FriendRequest>> GetFriendRequestsAsync(int userId)
        {
            try
            {
                return await _context.FriendRequests
                    .Include(fr => fr.FromUser)
                    .Where(fr => fr.ToUserId == userId && fr.Status == FriendRequestStatus.Pending)
                    .OrderByDescending(fr => fr.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting friend requests for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<UserDto>> GetFriendsAsync(int userId)
        {
            try
            {
                return await _context.Friendships
                    .Include(f => f.Friend)
                    .Where(f => f.UserId == userId)
                    .Select(f => new UserDto
                    {
                        Id = f.Friend.Id,
                        Username = f.Friend.Username,
                        FirstName = f.Friend.FirstName,
                        LastName = f.Friend.LastName,
                        Avatar = f.Friend.Avatar,
                        CreatedAt = f.Friend.CreatedAt,
                        LastLoginAt = f.Friend.LastLoginAt
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting friends for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> RemoveFriendAsync(int userId, int friendId)
        {
            try
            {
                var friendships = await _context.Friendships
                    .Where(f => (f.UserId == userId && f.FriendId == friendId) ||
                               (f.UserId == friendId && f.FriendId == userId))
                    .ToListAsync();

                if (!friendships.Any())
                {
                    throw new ArgumentException("Arkadaşlık bulunamadı.");
                }

                _context.Friendships.RemoveRange(friendships);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing friend {FriendId} for user {UserId}", friendId, userId);
                throw;
            }
        }
    }

    // DTOs
    public class UpdateProfileDto
    {
        public string? Username { get; set; }
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
    }

    public class UserSearchResult
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Avatar { get; set; }
    }
}
