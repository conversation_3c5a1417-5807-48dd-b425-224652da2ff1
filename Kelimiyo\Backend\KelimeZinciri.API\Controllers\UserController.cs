using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using KelimeZinciri.API.Services;
using System.Security.Claims;

namespace KelimeZinciri.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileDto updateProfileDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updatedUser = await _userService.UpdateProfileAsync(userId.Value, updateProfileDto);
                
                if (updatedUser == null)
                {
                    return NotFound(new { message = "Kullanıcı bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    user = updatedUser,
                    message = "Profil başarıyla güncellendi."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile");
                return StatusCode(500, new { message = "Profil güncellenirken hata oluştu." });
            }
        }

        [HttpPost("avatar")]
        public async Task<IActionResult> UploadAvatar([FromForm] IFormFile avatar)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var avatarUrl = await _userService.UploadAvatarAsync(userId.Value, avatar);
                
                if (avatarUrl == null)
                {
                    return NotFound(new { message = "Kullanıcı bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    avatarUrl = avatarUrl,
                    message = "Avatar başarıyla yüklendi."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading avatar");
                return StatusCode(500, new { message = "Avatar yüklenirken hata oluştu." });
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchUsers([FromQuery] string q, [FromQuery] int limit = 20)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    return BadRequest(new { message = "Arama terimi gerekli." });
                }

                if (limit <= 0 || limit > 50)
                {
                    limit = 20;
                }

                var users = await _userService.SearchUsersAsync(q, limit);
                
                return Ok(new
                {
                    success = true,
                    data = users,
                    count = users.Count,
                    query = q
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching users with query: {Query}", q);
                return StatusCode(500, new { message = "Kullanıcı arama sırasında hata oluştu." });
            }
        }

        [HttpPost("friend-request/{userId}")]
        public async Task<IActionResult> SendFriendRequest(int userId)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var success = await _userService.SendFriendRequestAsync(currentUserId.Value, userId);
                
                if (!success)
                {
                    return BadRequest(new { message = "Arkadaşlık isteği gönderilemedi." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Arkadaşlık isteği gönderildi."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending friend request to user {UserId}", userId);
                return StatusCode(500, new { message = "Arkadaşlık isteği gönderilirken hata oluştu." });
            }
        }

        [HttpPost("friend-request/{friendRequestId}/accept")]
        public async Task<IActionResult> AcceptFriendRequest(int friendRequestId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var success = await _userService.AcceptFriendRequestAsync(userId.Value, friendRequestId);
                
                if (!success)
                {
                    return BadRequest(new { message = "Arkadaşlık isteği kabul edilemedi." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Arkadaşlık isteği kabul edildi."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting friend request {FriendRequestId}", friendRequestId);
                return StatusCode(500, new { message = "Arkadaşlık isteği kabul edilirken hata oluştu." });
            }
        }

        [HttpPost("friend-request/{friendRequestId}/reject")]
        public async Task<IActionResult> RejectFriendRequest(int friendRequestId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var success = await _userService.RejectFriendRequestAsync(userId.Value, friendRequestId);
                
                if (!success)
                {
                    return BadRequest(new { message = "Arkadaşlık isteği reddedilemedi." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Arkadaşlık isteği reddedildi."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting friend request {FriendRequestId}", friendRequestId);
                return StatusCode(500, new { message = "Arkadaşlık isteği reddedilirken hata oluştu." });
            }
        }

        [HttpGet("friend-requests")]
        public async Task<IActionResult> GetFriendRequests()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var friendRequests = await _userService.GetFriendRequestsAsync(userId.Value);
                
                return Ok(new
                {
                    success = true,
                    data = friendRequests,
                    count = friendRequests.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting friend requests");
                return StatusCode(500, new { message = "Arkadaşlık istekleri alınırken hata oluştu." });
            }
        }

        [HttpGet("friends")]
        public async Task<IActionResult> GetFriends()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var friends = await _userService.GetFriendsAsync(userId.Value);
                
                return Ok(new
                {
                    success = true,
                    data = friends,
                    count = friends.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting friends");
                return StatusCode(500, new { message = "Arkadaş listesi alınırken hata oluştu." });
            }
        }

        [HttpDelete("friends/{friendId}")]
        public async Task<IActionResult> RemoveFriend(int friendId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var success = await _userService.RemoveFriendAsync(userId.Value, friendId);
                
                if (!success)
                {
                    return BadRequest(new { message = "Arkadaş silinemedi." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Arkadaş başarıyla silindi."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing friend {FriendId}", friendId);
                return StatusCode(500, new { message = "Arkadaş silinirken hata oluştu." });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }
    }
}
