@echo off
echo ========================================
echo    Kelime Zinciri <PERSON>şlatma
echo ========================================
echo.

echo Bu script hem backend hem frontend'i başlatır.
echo.
echo [1] Sadece Frontend Başlat
echo [2] Sadece Backend Başlat  
echo [3] Her İkisini de Başlat (Önerilen)
echo [4] PostgreSQL + Backend + Frontend
echo [5] Çıkış
echo.

set /p choice="Seçiminizi yapın (1-5): "

if "%choice%"=="1" goto frontend_only
if "%choice%"=="2" goto backend_only
if "%choice%"=="3" goto both
if "%choice%"=="4" goto full_stack
if "%choice%"=="5" goto exit
goto invalid

:frontend_only
echo.
echo Frontend başlatılıyor...
call start_frontend.bat
goto end

:backend_only
echo.
echo Backend başlatılıyor...
cd Backend\KelimeZinciri.API
dotnet run
goto end

:both
echo.
echo Backend ve Frontend başlatılıyor...
echo.
echo 1. Backend başlatılıyor (yeni pencerede)...
start "Kelime Zinciri Backend" cmd /k "cd Backend\KelimeZinciri.API && dotnet run"

echo.
echo 2. Frontend başlatılıyor (5 saniye sonra)...
timeout /t 5 /nobreak >nul
call start_frontend.bat
goto end

:full_stack
echo.
echo Full Stack başlatılıyor...
echo.
echo 1. PostgreSQL kontrol ediliyor...
docker ps | findstr postgres >nul 2>&1
if %errorLevel% neq 0 (
    echo PostgreSQL başlatılıyor...
    docker run --name kelime-postgres -d ^
        -e POSTGRES_DB=KelimeZinciriDB ^
        -e POSTGRES_USER=kelimiyo ^
        -e POSTGRES_PASSWORD=admin123 ^
        -p 5432:5432 ^
        postgres:16-alpine
    echo PostgreSQL başlatıldı. 10 saniye bekleniyor...
    timeout /t 10 /nobreak >nul
) else (
    echo ✓ PostgreSQL zaten çalışıyor
)

echo.
echo 2. Backend başlatılıyor (yeni pencerede)...
start "Kelime Zinciri Backend" cmd /k "cd Backend\KelimeZinciri.API && dotnet run"

echo.
echo 3. Frontend başlatılıyor (10 saniye sonra)...
timeout /t 10 /nobreak >nul
call start_frontend.bat
goto end

:invalid
echo.
echo Geçersiz seçim! Lütfen 1-5 arası bir sayı girin.
pause
goto start

:exit
echo.
echo Çıkılıyor...
goto end

:end
echo.
echo Script tamamlandı.
pause
exit /b 0
