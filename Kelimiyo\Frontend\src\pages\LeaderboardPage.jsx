import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, Crown, Star, Users, Calendar, Globe } from 'lucide-react';
import toast from 'react-hot-toast';

import { leaderboardAPI } from '../services/api';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const LeaderboardPage = () => {
  const [activeTab, setActiveTab] = useState('global');
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [myRank, setMyRank] = useState(null);

  useEffect(() => {
    loadLeaderboard();
    loadMyRank();
  }, [activeTab]);

  const loadLeaderboard = async () => {
    setIsLoading(true);
    try {
      let response;
      switch (activeTab) {
        case 'global':
          response = await leaderboardAPI.getGlobalLeaderboard();
          break;
        case 'weekly':
          response = await leaderboardAPI.getWeeklyLeaderboard();
          break;
        case 'monthly':
          response = await leaderboardAPI.getMonthlyLeaderboard();
          break;
        case 'friends':
          response = await leaderboardAPI.getFriendsLeaderboard();
          break;
        default:
          response = await leaderboardAPI.getGlobalLeaderboard();
      }

      if (response.success) {
        setLeaderboardData(response.data);
      }
    } catch (error) {
      console.error('Error loading leaderboard:', error);
      toast.error('Liderlik tablosu yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMyRank = async () => {
    try {
      const response = await leaderboardAPI.getMyRank();
      if (response.success) {
        setMyRank(response.data);
      }
    } catch (error) {
      console.error('Error loading my rank:', error);
    }
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Medal className="w-6 h-6 text-amber-600" />;
      default:
        return <span className="w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const tabs = [
    { id: 'global', label: 'Global', icon: Globe },
    { id: 'weekly', label: 'Haftalık', icon: Calendar },
    { id: 'monthly', label: 'Aylık', icon: Calendar },
    { id: 'friends', label: 'Arkadaşlar', icon: Users },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🏆 Liderlik Tablosu
          </h1>
          <p className="text-xl text-gray-600">
            En iyi oyuncuları keşfedin ve sıralamanızı görün
          </p>
        </motion.div>

        {/* My Rank Card */}
        {myRank && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6 mb-8"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Benim Sıralamam</h2>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getRankBadgeColor(myRank.rank)}`}>
                  {getRankIcon(myRank.rank)}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{myRank.username}</h3>
                  <p className="text-sm text-gray-600">#{myRank.rank} sırada</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-primary-600">{myRank.totalScore}</p>
                <p className="text-sm text-gray-600">Toplam Puan</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg mb-8"
        >
          <div className="flex border-b border-gray-200">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-4 px-6 font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon size={20} />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>

          {/* Leaderboard Content */}
          <div className="p-6">
            {isLoading ? (
              <div className="text-center py-12">
                <LoadingSpinner size="lg" text="Liderlik tablosu yükleniyor..." />
              </div>
            ) : leaderboardData.length > 0 ? (
              <div className="space-y-4">
                {leaderboardData.map((player, index) => (
                  <motion.div
                    key={player.userId}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={`flex items-center justify-between p-4 rounded-lg transition-colors ${
                      player.rank <= 3 ? 'bg-gradient-to-r from-yellow-50 to-orange-50' : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getRankBadgeColor(player.rank)}`}>
                        {getRankIcon(player.rank)}
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        {player.avatar ? (
                          <img
                            src={player.avatar}
                            alt={player.username}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold">
                            {player.username.charAt(0).toUpperCase()}
                          </div>
                        )}
                        
                        <div>
                          <h3 className="font-semibold text-gray-900">{player.username}</h3>
                          <p className="text-sm text-gray-600">
                            {player.gamesPlayed} oyun • %{player.winRate.toFixed(1)} kazanma oranı
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <p className="text-xl font-bold text-primary-600">{player.totalScore}</p>
                      <p className="text-sm text-gray-600">
                        {player.gamesWon} kazanım
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {activeTab === 'friends' ? 'Arkadaş bulunamadı' : 'Henüz veri yok'}
                </h3>
                <p className="text-gray-600">
                  {activeTab === 'friends' 
                    ? 'Arkadaş ekleyerek onların sıralamalarını görebilirsiniz.'
                    : 'Oyun oynayarak liderlik tablosunda yer alın!'
                  }
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default LeaderboardPage;
