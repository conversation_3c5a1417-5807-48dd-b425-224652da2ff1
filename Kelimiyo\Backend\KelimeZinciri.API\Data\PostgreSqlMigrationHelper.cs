using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace KelimeZinciri.API.Data
{
    public static class PostgreSqlMigrationHelper
    {
        public static async Task ApplyPostgreSqlOptimizationsAsync(ApplicationDbContext context)
        {
            try
            {
                // Check if tables exist before creating indexes
                var connection = context.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT COUNT(*) FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_name = 'users'
                ";

                var result = await command.ExecuteScalarAsync();
                var tablesExist = Convert.ToInt32(result) > 0;

                if (!tablesExist)
                {
                    Console.WriteLine("Tables not found, skipping index creation. Run migrations first.");
                    return;
                }

                // Create performance indexes
                await context.Database.ExecuteSqlRawAsync(@"
                    -- Create indexes for better performance
                    CREATE INDEX IF NOT EXISTS idx_users_username_lower ON users (<PERSON>OW<PERSON>(username));
                    CREATE INDEX IF NOT EXISTS idx_users_email_lower ON users (LOWER(email));
                    CREATE INDEX IF NOT EXISTS idx_users_active ON users (is_active) WHERE is_active = true;

                    CREATE INDEX IF NOT EXISTS idx_games_status ON games (status);
                    CREATE INDEX IF NOT EXISTS idx_games_created_at ON games (created_at);
                    CREATE INDEX IF NOT EXISTS idx_games_room_code_lower ON games (LOWER(room_code));

                    CREATE INDEX IF NOT EXISTS idx_words_text_gin ON words USING gin (text gin_trgm_ops);
                    CREATE INDEX IF NOT EXISTS idx_words_first_letter ON words (first_letter);
                    CREATE INDEX IF NOT EXISTS idx_words_last_letter ON words (last_letter);
                    CREATE INDEX IF NOT EXISTS idx_words_length ON words (length);
                    CREATE INDEX IF NOT EXISTS idx_words_category ON words (category);
                    CREATE INDEX IF NOT EXISTS idx_words_difficulty ON words (difficulty);
                    CREATE INDEX IF NOT EXISTS idx_words_active ON words (is_active) WHERE is_active = true;

                    CREATE INDEX IF NOT EXISTS idx_statistics_global_rank ON statistics (global_rank) WHERE global_rank > 0;
                    CREATE INDEX IF NOT EXISTS idx_statistics_weekly_rank ON statistics (weekly_rank) WHERE weekly_rank > 0;
                    CREATE INDEX IF NOT EXISTS idx_statistics_monthly_rank ON statistics (monthly_rank) WHERE monthly_rank > 0;
                    CREATE INDEX IF NOT EXISTS idx_statistics_total_score ON statistics (total_score DESC);

                    CREATE INDEX IF NOT EXISTS idx_game_moves_game_id ON game_moves (game_id);
                    CREATE INDEX IF NOT EXISTS idx_game_moves_user_id ON game_moves (user_id);
                    CREATE INDEX IF NOT EXISTS idx_game_moves_created_at ON game_moves (created_at);

                    CREATE INDEX IF NOT EXISTS idx_game_players_game_id ON game_players (game_id);
                    CREATE INDEX IF NOT EXISTS idx_game_players_user_id ON game_players (user_id);
                    CREATE INDEX IF NOT EXISTS idx_game_players_score ON game_players (score DESC);
                ");

                // Create triggers for automatic timestamp updates
                await context.Database.ExecuteSqlRawAsync(@"
                    -- Create trigger for statistics updated_at
                    DROP TRIGGER IF EXISTS update_statistics_updated_at ON statistics;
                    CREATE TRIGGER update_statistics_updated_at
                        BEFORE UPDATE ON statistics
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column();
                ");

                // Create materialized view for leaderboard performance
                await context.Database.ExecuteSqlRawAsync(@"
                    -- Create materialized view for global leaderboard
                    DROP MATERIALIZED VIEW IF EXISTS global_leaderboard;
                    CREATE MATERIALIZED VIEW global_leaderboard AS
                    SELECT
                        ROW_NUMBER() OVER (ORDER BY s.total_score DESC, s.games_won DESC, s.total_games_played ASC) as position,
                        u.id as user_id,
                        u.username,
                        u.avatar,
                        s.total_score,
                        s.games_won,
                        s.total_games_played,
                        CASE WHEN s.total_games_played > 0
                             THEN ROUND((s.games_won::DECIMAL / s.total_games_played * 100), 2)
                             ELSE 0
                        END as win_rate
                    FROM statistics s
                    INNER JOIN users u ON s.user_id = u.id
                    WHERE u.is_active = true AND s.total_games_played > 0
                    ORDER BY s.total_score DESC, s.games_won DESC, s.total_games_played ASC;

                    -- Create unique index on materialized view
                    CREATE UNIQUE INDEX IF NOT EXISTS idx_global_leaderboard_user_id ON global_leaderboard (user_id);
                    CREATE INDEX IF NOT EXISTS idx_global_leaderboard_position ON global_leaderboard (position);
                ");

                // Create function to refresh leaderboard
                await context.Database.ExecuteSqlRawAsync(@"
                    -- Function to refresh leaderboard materialized view
                    CREATE OR REPLACE FUNCTION refresh_leaderboard()
                    RETURNS void AS $$
                    BEGIN
                        REFRESH MATERIALIZED VIEW CONCURRENTLY global_leaderboard;
                    END;
                    $$ LANGUAGE plpgsql;
                ");

                // Create function for full-text search on words
                await context.Database.ExecuteSqlRawAsync(@"
                    -- Function for advanced word search
                    CREATE OR REPLACE FUNCTION search_words(
                        search_term TEXT,
                        search_category word_category DEFAULT NULL,
                        search_difficulty word_difficulty DEFAULT NULL,
                        limit_count INTEGER DEFAULT 50
                    )
                    RETURNS TABLE (
                        id INTEGER,
                        text VARCHAR(50),
                        length INTEGER,
                        first_letter CHAR(1),
                        last_letter CHAR(1),
                        category word_category,
                        difficulty word_difficulty,
                        base_points INTEGER,
                        frequency DECIMAL
                    ) AS $$
                    BEGIN
                        RETURN QUERY
                        SELECT
                            w.id,
                            w.text,
                            w.length,
                            w.first_letter,
                            w.last_letter,
                            w.category,
                            w.difficulty,
                            w.base_points,
                            w.frequency
                        FROM words w
                        WHERE w.is_active = true
                        AND (search_term IS NULL OR w.text ILIKE '%' || search_term || '%')
                        AND (search_category IS NULL OR w.category = search_category)
                        AND (search_difficulty IS NULL OR w.difficulty = search_difficulty)
                        ORDER BY
                            CASE WHEN w.text = search_term THEN 1 ELSE 2 END,
                            w.text <-> search_term,
                            w.frequency DESC
                        LIMIT limit_count;
                    END;
                    $$ LANGUAGE plpgsql;
                ");

                // Create function to update user rankings
                await context.Database.ExecuteSqlRawAsync(@"
                    -- Function to update user rankings
                    CREATE OR REPLACE FUNCTION update_user_rankings()
                    RETURNS void AS $$
                    BEGIN
                        -- Update global rankings
                        WITH ranked_users AS (
                            SELECT
                                user_id,
                                ROW_NUMBER() OVER (ORDER BY total_score DESC, games_won DESC, total_games_played ASC) as new_rank
                            FROM statistics s
                            INNER JOIN users u ON s.user_id = u.id
                            WHERE u.is_active = true AND s.total_games_played > 0
                        )
                        UPDATE statistics
                        SET global_rank = ranked_users.new_rank
                        FROM ranked_users
                        WHERE statistics.user_id = ranked_users.user_id;

                        -- Update weekly rankings
                        WITH weekly_ranked AS (
                            SELECT
                                user_id,
                                ROW_NUMBER() OVER (ORDER BY weekly_games_played DESC, total_score DESC) as new_rank
                            FROM statistics s
                            INNER JOIN users u ON s.user_id = u.id
                            WHERE u.is_active = true AND s.weekly_games_played > 0
                        )
                        UPDATE statistics
                        SET weekly_rank = weekly_ranked.new_rank
                        FROM weekly_ranked
                        WHERE statistics.user_id = weekly_ranked.user_id;

                        -- Update monthly rankings
                        WITH monthly_ranked AS (
                            SELECT
                                user_id,
                                ROW_NUMBER() OVER (ORDER BY monthly_games_played DESC, total_score DESC) as new_rank
                            FROM statistics s
                            INNER JOIN users u ON s.user_id = u.id
                            WHERE u.is_active = true AND s.monthly_games_played > 0
                        )
                        UPDATE statistics
                        SET monthly_rank = monthly_ranked.new_rank
                        FROM monthly_ranked
                        WHERE statistics.user_id = monthly_ranked.user_id;

                        -- Refresh leaderboard materialized view
                        PERFORM refresh_leaderboard();
                    END;
                    $$ LANGUAGE plpgsql;
                ");

                Console.WriteLine("PostgreSQL optimizations applied successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying PostgreSQL optimizations: {ex.Message}");
                throw;
            }
        }

        public static async Task<bool> TestConnectionAsync(string connectionString)
        {
            try
            {
                using var connection = new NpgsqlConnection(connectionString);
                await connection.OpenAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
