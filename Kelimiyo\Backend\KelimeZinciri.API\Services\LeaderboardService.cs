using Microsoft.EntityFrameworkCore;
using KelimeZinciri.API.Data;
using KelimeZinciri.API.Models;

namespace KelimeZinciri.API.Services
{
    public interface ILeaderboardService
    {
        Task<List<LeaderboardEntry>> GetGlobalLeaderboardAsync(int limit = 50);
        Task<List<LeaderboardEntry>> GetWeeklyLeaderboardAsync(int limit = 50);
        Task<List<LeaderboardEntry>> GetMonthlyLeaderboardAsync(int limit = 50);
        Task<LeaderboardEntry?> GetUserRankAsync(int userId);
        Task<List<LeaderboardEntry>> GetFriendsLeaderboardAsync(int userId, int limit = 20);
    }

    public class LeaderboardService : ILeaderboardService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<LeaderboardService> _logger;

        public LeaderboardService(ApplicationDbContext context, ILogger<LeaderboardService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<LeaderboardEntry>> GetGlobalLeaderboardAsync(int limit = 50)
        {
            try
            {
                var leaderboard = await _context.Statistics
                    .Include(s => s.User)
                    .Where(s => s.GamesPlayed > 0)
                    .OrderByDescending(s => s.TotalScore)
                    .ThenByDescending(s => s.GamesWon)
                    .ThenBy(s => s.GamesPlayed)
                    .Take(limit)
                    .Select((s, index) => new LeaderboardEntry
                    {
                        Rank = index + 1,
                        UserId = s.UserId,
                        Username = s.User.Username,
                        Avatar = s.User.Avatar,
                        TotalScore = s.TotalScore,
                        GamesPlayed = s.GamesPlayed,
                        GamesWon = s.GamesWon,
                        WinRate = s.GamesPlayed > 0 ? (double)s.GamesWon / s.GamesPlayed * 100 : 0,
                        BestScore = s.BestScore,
                        CurrentStreak = s.CurrentStreak,
                        BestStreak = s.BestStreak,
                        LastActive = s.UpdatedAt
                    })
                    .ToListAsync();

                // Update ranks properly
                for (int i = 0; i < leaderboard.Count; i++)
                {
                    leaderboard[i].Rank = i + 1;
                }

                return leaderboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting global leaderboard");
                throw;
            }
        }

        public async Task<List<LeaderboardEntry>> GetWeeklyLeaderboardAsync(int limit = 50)
        {
            try
            {
                var weekStart = DateTime.UtcNow.AddDays(-7);

                // For weekly leaderboard, we need to calculate scores from games played this week
                var weeklyStats = await _context.Games
                    .Include(g => g.GamePlayers)
                        .ThenInclude(gp => gp.User)
                    .Where(g => g.CreatedAt >= weekStart && g.Status == GameStatus.Completed)
                    .SelectMany(g => g.GamePlayers)
                    .GroupBy(gp => gp.UserId)
                    .Select(group => new
                    {
                        UserId = group.Key,
                        User = group.First().User,
                        WeeklyScore = group.Sum(gp => gp.Score),
                        WeeklyGames = group.Count(),
                        WeeklyWins = group.Count(gp => gp.IsWinner)
                    })
                    .OrderByDescending(x => x.WeeklyScore)
                    .ThenByDescending(x => x.WeeklyWins)
                    .Take(limit)
                    .ToListAsync();

                var leaderboard = weeklyStats.Select((stat, index) => new LeaderboardEntry
                {
                    Rank = index + 1,
                    UserId = stat.UserId,
                    Username = stat.User.Username,
                    Avatar = stat.User.Avatar,
                    TotalScore = stat.WeeklyScore,
                    GamesPlayed = stat.WeeklyGames,
                    GamesWon = stat.WeeklyWins,
                    WinRate = stat.WeeklyGames > 0 ? (double)stat.WeeklyWins / stat.WeeklyGames * 100 : 0,
                    BestScore = 0, // We don't track best score for weekly
                    CurrentStreak = 0, // We don't track streak for weekly
                    BestStreak = 0,
                    LastActive = DateTime.UtcNow
                }).ToList();

                return leaderboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting weekly leaderboard");
                throw;
            }
        }

        public async Task<List<LeaderboardEntry>> GetMonthlyLeaderboardAsync(int limit = 50)
        {
            try
            {
                var monthStart = DateTime.UtcNow.AddDays(-30);

                var monthlyStats = await _context.Games
                    .Include(g => g.GamePlayers)
                        .ThenInclude(gp => gp.User)
                    .Where(g => g.CreatedAt >= monthStart && g.Status == GameStatus.Completed)
                    .SelectMany(g => g.GamePlayers)
                    .GroupBy(gp => gp.UserId)
                    .Select(group => new
                    {
                        UserId = group.Key,
                        User = group.First().User,
                        MonthlyScore = group.Sum(gp => gp.Score),
                        MonthlyGames = group.Count(),
                        MonthlyWins = group.Count(gp => gp.IsWinner)
                    })
                    .OrderByDescending(x => x.MonthlyScore)
                    .ThenByDescending(x => x.MonthlyWins)
                    .Take(limit)
                    .ToListAsync();

                var leaderboard = monthlyStats.Select((stat, index) => new LeaderboardEntry
                {
                    Rank = index + 1,
                    UserId = stat.UserId,
                    Username = stat.User.Username,
                    Avatar = stat.User.Avatar,
                    TotalScore = stat.MonthlyScore,
                    GamesPlayed = stat.MonthlyGames,
                    GamesWon = stat.MonthlyWins,
                    WinRate = stat.MonthlyGames > 0 ? (double)stat.MonthlyWins / stat.MonthlyGames * 100 : 0,
                    BestScore = 0,
                    CurrentStreak = 0,
                    BestStreak = 0,
                    LastActive = DateTime.UtcNow
                }).ToList();

                return leaderboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly leaderboard");
                throw;
            }
        }

        public async Task<LeaderboardEntry?> GetUserRankAsync(int userId)
        {
            try
            {
                var userStats = await _context.Statistics
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (userStats == null)
                {
                    return null;
                }

                var rank = await _context.Statistics
                    .CountAsync(s => s.TotalScore > userStats.TotalScore) + 1;

                return new LeaderboardEntry
                {
                    Rank = rank,
                    UserId = userId,
                    Username = userStats.User.Username,
                    Avatar = userStats.User.Avatar,
                    TotalScore = userStats.TotalScore,
                    GamesPlayed = userStats.GamesPlayed,
                    GamesWon = userStats.GamesWon,
                    WinRate = userStats.GamesPlayed > 0 ? (double)userStats.GamesWon / userStats.GamesPlayed * 100 : 0,
                    BestScore = userStats.BestScore,
                    CurrentStreak = userStats.CurrentStreak,
                    BestStreak = userStats.BestStreak,
                    LastActive = userStats.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user rank for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<LeaderboardEntry>> GetFriendsLeaderboardAsync(int userId, int limit = 20)
        {
            try
            {
                // For now, return empty list as we haven't implemented friends system yet
                // In the future, this would get friends of the user and their stats
                return new List<LeaderboardEntry>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting friends leaderboard for user {UserId}", userId);
                throw;
            }
        }
    }

    // DTOs
    public class LeaderboardEntry
    {
        public int Rank { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string? Avatar { get; set; }
        public int TotalScore { get; set; }
        public int GamesPlayed { get; set; }
        public int GamesWon { get; set; }
        public double WinRate { get; set; }
        public int BestScore { get; set; }
        public int CurrentStreak { get; set; }
        public int BestStreak { get; set; }
        public DateTime LastActive { get; set; }
    }
}
