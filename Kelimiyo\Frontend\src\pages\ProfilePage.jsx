import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';
import { statisticsAPI, userAPI } from '../services/api';

const ProfilePage = () => {
  const { user, updateUser } = useAuth();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
  });

  useEffect(() => {
    loadUserStats();
  }, []);

  const loadUserStats = async () => {
    try {
      setLoading(true);
      const response = await statisticsAPI.getMyStats();
      setStats(response.data);
    } catch (error) {
      console.error('Stats load error:', error);
      toast.error('<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    try {
      await userAPI.updateProfile(formData);
      setEditing(false);
      toast.success('Profil güncellendi');
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error('Profil güncellenemedi');
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleAvatarUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Sadece JPEG, PNG ve GIF dosyaları kabul edilir');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Dosya boyutu 5MB\'dan büyük olamaz');
      return;
    }

    try {
      setUploadingAvatar(true);
      const response = await userAPI.uploadAvatar(file);

      if (response.success) {
        // Update user context with new avatar
        updateUser({ ...user, avatar: response.avatarUrl });
        toast.success('Avatar başarıyla güncellendi');
      } else {
        toast.error(response.message || 'Avatar yüklenemedi');
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      const errorMessage = error.response?.data?.message || 'Avatar yüklenirken hata oluştu';
      toast.error(errorMessage);
    } finally {
      setUploadingAvatar(false);
      // Clear the input
      e.target.value = '';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Profil yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 p-4"
    >
      <div className="max-w-4xl mx-auto">
        {/* Profile Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center space-x-6">
            <div className="relative">
              {user?.avatar ? (
                <img
                  src={`${import.meta.env.VITE_API_URL?.replace('/api', '') || 'https://localhost:7001'}${user.avatar}`}
                  alt="Avatar"
                  className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {user?.username?.charAt(0).toUpperCase()}
                </div>
              )}

              {/* Avatar Upload Button */}
              <label
                className={`absolute bottom-0 right-0 bg-primary-600 text-white rounded-full p-1 cursor-pointer hover:bg-primary-700 transition-colors ${uploadingAvatar ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Avatar değiştir"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <input
                  type="file"
                  accept="image/jpeg,image/jpg,image/png,image/gif"
                  onChange={handleAvatarUpload}
                  className="hidden"
                  disabled={uploadingAvatar}
                />
              </label>

              {uploadingAvatar && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                </div>
              )}
            </div>

            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800">{user?.username}</h1>
              <p className="text-gray-600">{user?.email}</p>
              <p className="text-sm text-gray-500">
                Üyelik: {new Date(user?.createdAt).toLocaleDateString('tr-TR')}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                💡 Avatar değiştirmek için fotoğraf simgesine tıklayın (Max: 5MB, JPEG/PNG/GIF)
              </p>
            </div>
            <button
              onClick={() => setEditing(!editing)}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700"
            >
              {editing ? 'İptal' : 'Düzenle'}
            </button>
          </div>
        </div>

        {/* Edit Profile Form */}
        {editing && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-lg p-6 mb-6"
          >
            <h2 className="text-xl font-semibold mb-4">Profili Düzenle</h2>
            <form onSubmit={handleUpdateProfile} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kullanıcı Adı
                </label>
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  E-posta
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  required
                />
              </div>
              <div className="flex space-x-4">
                <button
                  type="submit"
                  className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700"
                >
                  Kaydet
                </button>
                <button
                  type="button"
                  onClick={() => setEditing(false)}
                  className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600"
                >
                  İptal
                </button>
              </div>
            </form>
          </motion.div>
        )}

        {/* Statistics */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-6">İstatistikler</h2>

          {stats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {stats.gamesPlayed || 0}
                </div>
                <div className="text-sm text-gray-600">Toplam Oyun</div>
              </div>

              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {stats.gamesWon || 0}
                </div>
                <div className="text-sm text-gray-600">Kazanılan Oyun</div>
              </div>

              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.totalScore || 0}
                </div>
                <div className="text-sm text-gray-600">Toplam Skor</div>
              </div>

              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {stats.winRate ? `${stats.winRate.toFixed(1)}%` : '0%'}
                </div>
                <div className="text-sm text-gray-600">Kazanma Oranı</div>
              </div>

              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {stats.currentStreak || 0}
                </div>
                <div className="text-sm text-gray-600">Mevcut Seri</div>
              </div>

              <div className="text-center p-4 bg-indigo-50 rounded-lg">
                <div className="text-2xl font-bold text-indigo-600">
                  {stats.bestStreak || 0}
                </div>
                <div className="text-sm text-gray-600">En Uzun Seri</div>
              </div>

              <div className="text-center p-4 bg-pink-50 rounded-lg">
                <div className="text-2xl font-bold text-pink-600">
                  {stats.totalWordsUsed || 0}
                </div>
                <div className="text-sm text-gray-600">Kullanılan Kelime</div>
              </div>

              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {stats.bestScore || 0}
                </div>
                <div className="text-sm text-gray-600">En Yüksek Skor</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">Henüz istatistik bulunmuyor.</p>
              <p className="text-sm text-gray-500 mt-2">
                Oyun oynamaya başlayın ve istatistiklerinizi görün!
              </p>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ProfilePage;
