using Microsoft.EntityFrameworkCore;
using KelimeZinciri.API.Data;
using KelimeZinciri.API.Models;

namespace KelimeZinciri.API.Services
{
    public interface IStatisticsService
    {
        Task<UserStatistics> GetUserStatisticsAsync(int userId);
        Task<UserStatistics> GetMyStatisticsAsync(int userId);
        Task UpdateGameStatisticsAsync(int userId, GameResult gameResult);
        Task<List<Achievement>> GetUserAchievementsAsync(int userId);
        Task CheckAndAwardAchievementsAsync(int userId);
    }

    public class StatisticsService : IStatisticsService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<StatisticsService> _logger;

        public StatisticsService(ApplicationDbContext context, ILogger<StatisticsService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<UserStatistics> GetUserStatisticsAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException("Kullanıcı bulunamadı.");
                }

                var stats = await _context.Statistics
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (stats == null)
                {
                    // Create default statistics if not exists
                    stats = new Statistics
                    {
                        UserId = userId,
                        GamesPlayed = 0,
                        GamesWon = 0,
                        TotalScore = 0,
                        BestScore = 0,
                        TotalWordsUsed = 0,
                        AverageWordLength = 0,
                        LongestWord = "",
                        FastestGameTime = 0,
                        CurrentStreak = 0,
                        BestStreak = 0,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.Statistics.Add(stats);
                    await _context.SaveChangesAsync();
                }

                return new UserStatistics
                {
                    UserId = userId,
                    Username = user.Username,
                    Avatar = user.Avatar,
                    GamesPlayed = stats.GamesPlayed,
                    GamesWon = stats.GamesWon,
                    WinRate = stats.GamesPlayed > 0 ? (double)stats.GamesWon / stats.GamesPlayed * 100 : 0,
                    TotalScore = stats.TotalScore,
                    BestScore = stats.BestScore,
                    AverageScore = stats.GamesPlayed > 0 ? (double)stats.TotalScore / stats.GamesPlayed : 0,
                    TotalWordsUsed = stats.TotalWordsUsed,
                    AverageWordLength = stats.AverageWordLength,
                    LongestWord = stats.LongestWord,
                    FastestGameTime = stats.FastestGameTime,
                    CurrentStreak = stats.CurrentStreak,
                    BestStreak = stats.BestStreak,
                    Rank = await GetUserRankAsync(userId),
                    CreatedAt = stats.CreatedAt,
                    UpdatedAt = stats.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user statistics for user {UserId}", userId);
                throw;
            }
        }

        public async Task<UserStatistics> GetMyStatisticsAsync(int userId)
        {
            return await GetUserStatisticsAsync(userId);
        }

        public async Task UpdateGameStatisticsAsync(int userId, GameResult gameResult)
        {
            try
            {
                var stats = await _context.Statistics
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (stats == null)
                {
                    stats = new Statistics
                    {
                        UserId = userId,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.Statistics.Add(stats);
                }

                // Update statistics
                stats.GamesPlayed++;
                stats.TotalScore += gameResult.Score;
                stats.TotalWordsUsed += gameResult.WordsUsed;

                if (gameResult.IsWin)
                {
                    stats.GamesWon++;
                    stats.CurrentStreak++;
                    if (stats.CurrentStreak > stats.BestStreak)
                    {
                        stats.BestStreak = stats.CurrentStreak;
                    }
                }
                else
                {
                    stats.CurrentStreak = 0;
                }

                if (gameResult.Score > stats.BestScore)
                {
                    stats.BestScore = gameResult.Score;
                }

                if (!string.IsNullOrEmpty(gameResult.LongestWord) && 
                    gameResult.LongestWord.Length > stats.LongestWord.Length)
                {
                    stats.LongestWord = gameResult.LongestWord;
                }

                if (gameResult.GameTime > 0 && 
                    (stats.FastestGameTime == 0 || gameResult.GameTime < stats.FastestGameTime))
                {
                    stats.FastestGameTime = gameResult.GameTime;
                }

                // Calculate average word length
                if (stats.TotalWordsUsed > 0)
                {
                    stats.AverageWordLength = (double)gameResult.TotalWordLength / stats.TotalWordsUsed;
                }

                stats.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Check for achievements
                await CheckAndAwardAchievementsAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating game statistics for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<Achievement>> GetUserAchievementsAsync(int userId)
        {
            try
            {
                return await _context.Achievements
                    .Where(a => a.UserId == userId)
                    .OrderByDescending(a => a.UnlockedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user achievements for user {UserId}", userId);
                throw;
            }
        }

        public async Task CheckAndAwardAchievementsAsync(int userId)
        {
            try
            {
                var stats = await _context.Statistics
                    .FirstOrDefaultAsync(s => s.UserId == userId);

                if (stats == null) return;

                var existingAchievements = await _context.Achievements
                    .Where(a => a.UserId == userId)
                    .Select(a => a.Type)
                    .ToListAsync();

                var newAchievements = new List<Achievement>();

                // First Win Achievement
                if (stats.GamesWon >= 1 && !existingAchievements.Contains("FIRST_WIN"))
                {
                    newAchievements.Add(new Achievement
                    {
                        UserId = userId,
                        Type = "FIRST_WIN",
                        Title = "İlk Zafer",
                        Description = "İlk oyununuzu kazandınız!",
                        Icon = "🏆",
                        UnlockedAt = DateTime.UtcNow
                    });
                }

                // Win Streak Achievements
                if (stats.BestStreak >= 5 && !existingAchievements.Contains("WIN_STREAK_5"))
                {
                    newAchievements.Add(new Achievement
                    {
                        UserId = userId,
                        Type = "WIN_STREAK_5",
                        Title = "Seri Kazanan",
                        Description = "5 oyun üst üste kazandınız!",
                        Icon = "🔥",
                        UnlockedAt = DateTime.UtcNow
                    });
                }

                // Games Played Achievements
                if (stats.GamesPlayed >= 10 && !existingAchievements.Contains("GAMES_10"))
                {
                    newAchievements.Add(new Achievement
                    {
                        UserId = userId,
                        Type = "GAMES_10",
                        Title = "Deneyimli Oyuncu",
                        Description = "10 oyun oynadınız!",
                        Icon = "🎮",
                        UnlockedAt = DateTime.UtcNow
                    });
                }

                // High Score Achievements
                if (stats.BestScore >= 1000 && !existingAchievements.Contains("HIGH_SCORE_1000"))
                {
                    newAchievements.Add(new Achievement
                    {
                        UserId = userId,
                        Type = "HIGH_SCORE_1000",
                        Title = "Yüksek Skor",
                        Description = "1000 puan aldınız!",
                        Icon = "⭐",
                        UnlockedAt = DateTime.UtcNow
                    });
                }

                if (newAchievements.Any())
                {
                    _context.Achievements.AddRange(newAchievements);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking achievements for user {UserId}", userId);
            }
        }

        private async Task<int> GetUserRankAsync(int userId)
        {
            try
            {
                var userScore = await _context.Statistics
                    .Where(s => s.UserId == userId)
                    .Select(s => s.TotalScore)
                    .FirstOrDefaultAsync();

                var rank = await _context.Statistics
                    .CountAsync(s => s.TotalScore > userScore) + 1;

                return rank;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user rank for user {UserId}", userId);
                return 0;
            }
        }
    }

    // DTOs
    public class UserStatistics
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string? Avatar { get; set; }
        public int GamesPlayed { get; set; }
        public int GamesWon { get; set; }
        public double WinRate { get; set; }
        public int TotalScore { get; set; }
        public int BestScore { get; set; }
        public double AverageScore { get; set; }
        public int TotalWordsUsed { get; set; }
        public double AverageWordLength { get; set; }
        public string LongestWord { get; set; } = string.Empty;
        public int FastestGameTime { get; set; }
        public int CurrentStreak { get; set; }
        public int BestStreak { get; set; }
        public int Rank { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class GameResult
    {
        public int Score { get; set; }
        public bool IsWin { get; set; }
        public int WordsUsed { get; set; }
        public int TotalWordLength { get; set; }
        public string LongestWord { get; set; } = string.Empty;
        public int GameTime { get; set; }
    }
}
