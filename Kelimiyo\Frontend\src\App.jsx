import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';

// Services
import { authAPI, setAuthToken, clearAuth } from './services/api';
import signalRService from './services/signalr';

// Pages
import HomePage from './pages/Home';
import GamePage from './pages/GamePage';
import ProfilePage from './pages/ProfilePage';
import LeaderboardPage from './pages/LeaderboardPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';

// Components
import Navbar from './components/Layout/Navbar';
import LoadingSpinner from './components/UI/LoadingSpinner';

// Context
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { GameProvider } from './contexts/GameContext';

function AppContent() {
  const { user, login, logout, isLoading } = useAuth();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      const token = localStorage.getItem('token');
      const savedUser = localStorage.getItem('user');

      if (token && savedUser) {
        setAuthToken(token);

        try {
          // Validate token with server
          const response = await authAPI.validateToken();
          if (response.success) {
            login(response.user, token);

            // Connect to SignalR
            await signalRService.connect(token);
          } else {
            clearAuth();
          }
        } catch (error) {
          console.error('Token validation failed:', error);
          clearAuth();
        }
      }
    } catch (error) {
      console.error('App initialization failed:', error);
      clearAuth();
    } finally {
      setIsInitializing(false);
    }
  };

  const handleLogout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      await signalRService.disconnect();
      logout();
      clearAuth();
      toast.success('Başarıyla çıkış yaptınız');
    }
  };

  if (isInitializing || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Kelime Zinciri yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      {user && <Navbar user={user} onLogout={handleLogout} />}

      <main className={user ? 'pt-16' : ''}>
        <AnimatePresence mode="wait">
          <Routes>
            {/* Public routes */}
            <Route
              path="/login"
              element={
                !user ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LoginPage />
                  </motion.div>
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />

            <Route
              path="/register"
              element={
                !user ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <RegisterPage />
                  </motion.div>
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />

            {/* Protected routes */}
            <Route
              path="/"
              element={
                user ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <HomePage />
                  </motion.div>
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />

            <Route
              path="/game/:roomCode?"
              element={
                user ? (
                  <GameProvider>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.3 }}
                    >
                      <GamePage />
                    </motion.div>
                  </GameProvider>
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />

            <Route
              path="/profile"
              element={
                user ? (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ProfilePage />
                  </motion.div>
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />

            <Route
              path="/leaderboard"
              element={
                user ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LeaderboardPage />
                  </motion.div>
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />

            {/* Catch all route */}
            <Route
              path="*"
              element={
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
                    <p className="text-gray-600 mb-8">Sayfa bulunamadı</p>
                    <button
                      onClick={() => window.history.back()}
                      className="btn-primary"
                    >
                      Geri Dön
                    </button>
                  </div>
                </div>
              }
            />
          </Routes>
        </AnimatePresence>
      </main>

      {/* Global error boundary could be added here */}
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
