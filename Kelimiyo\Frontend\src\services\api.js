import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'https://localhost:7001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      toast.error('Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.');
    } else if (error.code === 'ECONNABORTED') {
      toast.error('İstek zaman aşımına uğradı. Lütfen tekrar deneyin.');
    } else if (!error.response) {
      toast.error('Bağlantı hatası. İnternet bağlantınızı kontrol edin.');
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  validateToken: async () => {
    const response = await api.post('/auth/validate-token');
    return response.data;
  },

  refreshToken: async () => {
    const response = await api.post('/auth/refresh-token');
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },
};

// Game API
export const gameAPI = {
  createRoom: async (gameData) => {
    const response = await api.post('/game/create-room', gameData);
    return response.data;
  },

  joinRoom: async (roomCode) => {
    const response = await api.post(`/game/join-room/${roomCode}`);
    return response.data;
  },

  startGame: async (gameId) => {
    const response = await api.post(`/game/${gameId}/start`);
    return response.data;
  },

  submitWord: async (gameId, word) => {
    const response = await api.post(`/game/${gameId}/submit-word`, { word });
    return response.data;
  },

  validateWord: async (word, requiredStartLetter = null) => {
    const params = requiredStartLetter ? `?requiredStartLetter=${requiredStartLetter}` : '';
    const response = await api.get(`/game/validate-word/${word}${params}`);
    return response.data;
  },

  getGame: async (gameId) => {
    const response = await api.get(`/game/${gameId}`);
    return response.data;
  },

  getGameByRoomCode: async (roomCode) => {
    const response = await api.get(`/game/room/${roomCode}`);
    return response.data;
  },

  leaveGame: async (gameId) => {
    const response = await api.post(`/game/${gameId}/leave`);
    return response.data;
  },

  getActiveGames: async () => {
    const response = await api.get('/game/active');
    return response.data;
  },

  getMyGames: async (page = 1, pageSize = 10) => {
    const response = await api.get(`/game/my-games?page=${page}&pageSize=${pageSize}`);
    return response.data;
  },

  getRandomWords: async (count = 10, difficulty = null) => {
    const params = new URLSearchParams();
    params.append('count', count);
    if (difficulty) params.append('difficulty', difficulty);

    const response = await api.get(`/game/random-words?${params}`);
    return response.data;
  },

  addCustomWord: async (word, definition = null) => {
    const response = await api.post('/game/add-custom-word', { word, definition });
    return response.data;
  },
};

// Statistics API
export const statisticsAPI = {
  getUserStats: async (userId) => {
    const response = await api.get(`/statistics/user/${userId}`);
    return response.data;
  },

  getMyStats: async () => {
    const response = await api.get('/statistics/my');
    return response.data;
  },

  updateGameResult: async (gameResult) => {
    const response = await api.post('/statistics/update-game-result', gameResult);
    return response.data;
  },

  getMyAchievements: async () => {
    const response = await api.get('/statistics/achievements');
    return response.data;
  },

  getUserAchievements: async (userId) => {
    const response = await api.get(`/statistics/achievements/${userId}`);
    return response.data;
  },

  checkAchievements: async () => {
    const response = await api.post('/statistics/check-achievements');
    return response.data;
  },
};

// Leaderboard API
export const leaderboardAPI = {
  getGlobalLeaderboard: async (limit = 50) => {
    const response = await api.get(`/leaderboard/global?limit=${limit}`);
    return response.data;
  },

  getWeeklyLeaderboard: async (limit = 50) => {
    const response = await api.get(`/leaderboard/weekly?limit=${limit}`);
    return response.data;
  },

  getMonthlyLeaderboard: async (limit = 50) => {
    const response = await api.get(`/leaderboard/monthly?limit=${limit}`);
    return response.data;
  },

  getMyRank: async () => {
    const response = await api.get('/leaderboard/my-rank');
    return response.data;
  },

  getUserRank: async (userId) => {
    const response = await api.get(`/leaderboard/user/${userId}/rank`);
    return response.data;
  },

  getFriendsLeaderboard: async (limit = 20) => {
    const response = await api.get(`/leaderboard/friends?limit=${limit}`);
    return response.data;
  },

  getTopPlayers: async (limit = 10) => {
    const response = await api.get(`/leaderboard/top-players?limit=${limit}`);
    return response.data;
  },
};

// User API
export const userAPI = {
  updateProfile: async (userData) => {
    const response = await api.put('/user/profile', userData);
    return response.data;
  },

  uploadAvatar: async (file) => {
    const formData = new FormData();
    formData.append('File', file); // Backend'te 'File' property'si bekleniyor

    const response = await api.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  searchUsers: async (query) => {
    const response = await api.get(`/user/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },

  sendFriendRequest: async (userId) => {
    const response = await api.post(`/user/friend-request/${userId}`);
    return response.data;
  },

  acceptFriendRequest: async (requestId) => {
    const response = await api.post(`/user/friend-request/${requestId}/accept`);
    return response.data;
  },

  rejectFriendRequest: async (requestId) => {
    const response = await api.post(`/user/friend-request/${requestId}/reject`);
    return response.data;
  },

  getFriends: async () => {
    const response = await api.get('/user/friends');
    return response.data;
  },

  getFriendRequests: async () => {
    const response = await api.get('/user/friend-requests');
    return response.data;
  },

  removeFriend: async (friendId) => {
    const response = await api.delete(`/user/friends/${friendId}`);
    return response.data;
  },
};

// Utility functions
export const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('token', token);
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
  }
};

export const getAuthToken = () => {
  return localStorage.getItem('token');
};

export const isAuthenticated = () => {
  const token = getAuthToken();
  return !!token;
};

export const clearAuth = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  delete api.defaults.headers.common['Authorization'];
};

export default api;
