using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using KelimeZinciri.API.Services;
using KelimeZinciri.API.Models.DTOs;
using System.Security.Claims;

namespace KelimeZinciri.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _authService.LoginAsync(loginDto.Email, loginDto.Password);

                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(new
                {
                    success = true,
                    token = result.Token,
                    user = result.User,
                    message = "G<PERSON><PERSON> başarılı."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login attempt for email: {Email}", loginDto.Email);
                return StatusCode(500, new { message = "Sunucu hatası oluştu." });
            }
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterDto registerDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Basic validation
                if (string.IsNullOrWhiteSpace(registerDto.Username) || registerDto.Username.Length < 3)
                {
                    return BadRequest(new { message = "Kullanıcı adı en az 3 karakter olmalıdır." });
                }

                if (string.IsNullOrWhiteSpace(registerDto.Password) || registerDto.Password.Length < 6)
                {
                    return BadRequest(new { message = "Şifre en az 6 karakter olmalıdır." });
                }

                if (!IsValidEmail(registerDto.Email))
                {
                    return BadRequest(new { message = "Geçerli bir e-posta adresi giriniz." });
                }

                var result = await _authService.RegisterAsync(registerDto);

                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(new
                {
                    success = true,
                    token = result.Token,
                    user = result.User,
                    message = "Kayıt başarılı."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email: {Email}", registerDto.Email);
                return StatusCode(500, new { message = "Sunucu hatası oluştu." });
            }
        }



        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var user = await _authService.GetUserFromTokenAsync(token);

                if (user == null)
                {
                    return NotFound(new { message = "Kullanıcı bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    user = new UserDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Avatar = user.Avatar,
                        CreatedAt = user.CreatedAt,
                        LastLoginAt = user.LastLoginAt
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user profile");
                return StatusCode(500, new { message = "Sunucu hatası oluştu." });
            }
        }

        [HttpPost("validate-token")]
        [Authorize]
        public async Task<IActionResult> ValidateToken()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Geçersiz token." });
                }

                var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var user = await _authService.GetUserFromTokenAsync(token);

                if (user == null)
                {
                    return Unauthorized(new { message = "Kullanıcı bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    user = new UserDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Avatar = user.Avatar,
                        CreatedAt = user.CreatedAt,
                        LastLoginAt = user.LastLoginAt
                    },
                    message = "Token geçerli."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                return Unauthorized(new { message = "Token doğrulanamadı." });
            }
        }

        [HttpPost("refresh-token")]
        [Authorize]
        public async Task<IActionResult> RefreshToken()
        {
            try
            {
                var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var result = await _authService.RefreshTokenAsync(token);

                if (!result.Success)
                {
                    return Unauthorized(new { message = result.Message });
                }

                return Ok(new
                {
                    success = true,
                    token = result.Token,
                    user = result.User,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return StatusCode(500, new { message = "Token yenilenemedi." });
            }
        }

        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var success = await _authService.LogoutAsync(token);

                if (!success)
                {
                    return BadRequest(new { message = "Çıkış yapılamadı." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Başarıyla çıkış yapıldı."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { message = "Çıkış yapılırken hata oluştu." });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }

}
