using System.ComponentModel.DataAnnotations;

namespace KelimeZinciri.API.Models
{
    public class Game
    {
        public int Id { get; set; }

        [Required]
        public string RoomCode { get; set; } = string.Empty;

        public int CreatedByUserId { get; set; }
        public GameDifficulty Difficulty { get; set; } = GameDifficulty.Easy;
        public GameStatus Status { get; set; } = GameStatus.Waiting;

        public int MaxPlayers { get; set; } = 4;
        public int CurrentPlayerCount { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? StartedAt { get; set; }
        public DateTime? EndedAt { get; set; }

        public int TimeLimit { get; set; } = 30; // seconds
        public int CurrentRound { get; set; } = 1;
        public int MaxRounds { get; set; } = 10;

        public string? CurrentWord { get; set; }
        public char? RequiredStartLetter { get; set; }
        public int? CurrentPlayerTurnId { get; set; }

        // Navigation properties
        public virtual User CreatedByUser { get; set; } = null!;
        public virtual ICollection<GamePlayer> GamePlayers { get; set; } = new List<GamePlayer>();
        public virtual ICollection<GameMove> Moves { get; set; } = new List<GameMove>();
    }

    public class GamePlayer
    {
        public int Id { get; set; }
        public int GameId { get; set; }
        public int UserId { get; set; }

        public int Score { get; set; } = 0;
        public int Position { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsWinner { get; set; } = false;
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        public virtual Game Game { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    public class GameMove
    {
        public int Id { get; set; }
        public int GameId { get; set; }
        public int UserId { get; set; }

        public string Word { get; set; } = string.Empty;
        public int Round { get; set; }
        public int Points { get; set; }
        public bool IsValid { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ResponseTime { get; set; }

        public virtual Game Game { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    public enum GameDifficulty
    {
        Easy = 1,
        Medium = 2,
        Hard = 3
    }

    public enum GameStatus
    {
        Waiting = 0,
        InProgress = 1,
        Completed = 2,
        Finished = 2, // Alias for Completed
        Cancelled = 3
    }
}
