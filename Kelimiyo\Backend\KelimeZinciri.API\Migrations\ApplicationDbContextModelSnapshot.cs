﻿// <auto-generated />
using System;
using KelimeZinciri.API.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace KelimeZinciri.API.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pg_trgm");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("KelimeZinciri.API.Models.Achievement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Points")
                        .HasColumnType("integer");

                    b.Property<int>("RequiredValue")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Achievements");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.CustomWord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Definition")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ReviewNotes")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("ReviewedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ReviewedByUserId")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ReviewedByUserId");

                    b.HasIndex("UserId");

                    b.ToTable("CustomWords");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Game", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("integer");

                    b.Property<int>("CurrentPlayerCount")
                        .HasColumnType("integer");

                    b.Property<int?>("CurrentPlayerTurnId")
                        .HasColumnType("integer");

                    b.Property<int>("CurrentRound")
                        .HasColumnType("integer");

                    b.Property<string>("CurrentWord")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Difficulty")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MaxPlayers")
                        .HasColumnType("integer");

                    b.Property<int>("MaxRounds")
                        .HasColumnType("integer");

                    b.Property<char?>("RequiredStartLetter")
                        .HasColumnType("character(1)");

                    b.Property<string>("RoomCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TimeLimit")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("RoomCode")
                        .IsUnique();

                    b.ToTable("Games");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.GameMove", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("GameId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsValid")
                        .HasColumnType("boolean");

                    b.Property<int>("Points")
                        .HasColumnType("integer");

                    b.Property<TimeSpan>("ResponseTime")
                        .HasColumnType("interval");

                    b.Property<int>("Round")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("Word")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("GameId");

                    b.HasIndex("UserId");

                    b.ToTable("GameMoves");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.GamePlayer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("GameId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("JoinedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Position")
                        .HasColumnType("integer");

                    b.Property<int>("Score")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("GameId", "UserId")
                        .IsUnique();

                    b.ToTable("GamePlayers");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Statistics", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AchievementBadges")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<TimeSpan>("AverageResponseTime")
                        .HasColumnType("interval");

                    b.Property<double>("AverageScore")
                        .HasPrecision(10, 2)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CurrentLossStreak")
                        .HasColumnType("integer");

                    b.Property<int>("CurrentWinStreak")
                        .HasColumnType("integer");

                    b.Property<int>("DailyGamesPlayed")
                        .HasColumnType("integer");

                    b.Property<int>("EasyGamesWon")
                        .HasColumnType("integer");

                    b.Property<TimeSpan>("FastestResponse")
                        .HasColumnType("interval");

                    b.Property<int>("GamesDraw")
                        .HasColumnType("integer");

                    b.Property<int>("GamesLost")
                        .HasColumnType("integer");

                    b.Property<int>("GamesWon")
                        .HasColumnType("integer");

                    b.Property<int>("GlobalRank")
                        .HasColumnType("integer");

                    b.Property<int>("HardGamesWon")
                        .HasColumnType("integer");

                    b.Property<int>("HighestScore")
                        .HasColumnType("integer");

                    b.Property<int>("InvalidWords")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastGameDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("LongestWinStreak")
                        .HasColumnType("integer");

                    b.Property<int>("MediumGamesWon")
                        .HasColumnType("integer");

                    b.Property<int>("MonthlyGamesPlayed")
                        .HasColumnType("integer");

                    b.Property<int>("MonthlyRank")
                        .HasColumnType("integer");

                    b.Property<int>("TotalAchievements")
                        .HasColumnType("integer");

                    b.Property<int>("TotalGamesPlayed")
                        .HasColumnType("integer");

                    b.Property<TimeSpan>("TotalPlayTime")
                        .HasColumnType("interval");

                    b.Property<int>("TotalScore")
                        .HasColumnType("integer");

                    b.Property<int>("TotalWordsUsed")
                        .HasColumnType("integer");

                    b.Property<int>("UniqueWords")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<int>("ValidWords")
                        .HasColumnType("integer");

                    b.Property<int>("WeeklyGamesPlayed")
                        .HasColumnType("integer");

                    b.Property<int>("WeeklyRank")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Statistics");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Avatar")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.UserAchievement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AchievementId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<int>("Progress")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AchievementId");

                    b.HasIndex("UserId", "AchievementId")
                        .IsUnique();

                    b.ToTable("UserAchievements");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.UserFriend", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FriendId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsAccepted")
                        .HasColumnType("boolean");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FriendId");

                    b.HasIndex("UserId", "FriendId")
                        .IsUnique();

                    b.ToTable("UserFriends");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Word", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BasePoints")
                        .HasColumnType("integer");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Difficulty")
                        .HasColumnType("integer");

                    b.Property<char>("FirstLetter")
                        .HasColumnType("character(1)");

                    b.Property<double>("Frequency")
                        .HasPrecision(10, 6)
                        .HasColumnType("double precision");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<char>("LastLetter")
                        .HasColumnType("character(1)");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("UsageCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FirstLetter");

                    b.HasIndex("LastLetter");

                    b.HasIndex("Text")
                        .IsUnique();

                    b.HasIndex("FirstLetter", "Length");

                    b.ToTable("Words");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.WordValidation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsValid")
                        .HasColumnType("boolean");

                    b.Property<string>("Reason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("ValidatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ValidatedByUserId")
                        .HasColumnType("integer");

                    b.Property<string>("Word")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ValidatedByUserId");

                    b.ToTable("WordValidations");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.CustomWord", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.User", "ReviewedByUser")
                        .WithMany()
                        .HasForeignKey("ReviewedByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("KelimeZinciri.API.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ReviewedByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Game", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.User", "CreatedByUser")
                        .WithMany("Games")
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.GameMove", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.Game", "Game")
                        .WithMany("Moves")
                        .HasForeignKey("GameId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KelimeZinciri.API.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Game");

                    b.Navigation("User");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.GamePlayer", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.Game", "Game")
                        .WithMany("Players")
                        .HasForeignKey("GameId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KelimeZinciri.API.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Game");

                    b.Navigation("User");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Statistics", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.User", "User")
                        .WithOne("Statistics")
                        .HasForeignKey("KelimeZinciri.API.Models.Statistics", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.UserAchievement", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.Achievement", "Achievement")
                        .WithMany("UserAchievements")
                        .HasForeignKey("AchievementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KelimeZinciri.API.Models.User", "User")
                        .WithMany("UserAchievements")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Achievement");

                    b.Navigation("User");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.UserFriend", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.User", "Friend")
                        .WithMany("FriendOf")
                        .HasForeignKey("FriendId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("KelimeZinciri.API.Models.User", "User")
                        .WithMany("Friends")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Friend");

                    b.Navigation("User");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.WordValidation", b =>
                {
                    b.HasOne("KelimeZinciri.API.Models.User", "ValidatedByUser")
                        .WithMany()
                        .HasForeignKey("ValidatedByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ValidatedByUser");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Achievement", b =>
                {
                    b.Navigation("UserAchievements");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.Game", b =>
                {
                    b.Navigation("Moves");

                    b.Navigation("Players");
                });

            modelBuilder.Entity("KelimeZinciri.API.Models.User", b =>
                {
                    b.Navigation("FriendOf");

                    b.Navigation("Friends");

                    b.Navigation("Games");

                    b.Navigation("Statistics");

                    b.Navigation("UserAchievements");
                });
#pragma warning restore 612, 618
        }
    }
}
