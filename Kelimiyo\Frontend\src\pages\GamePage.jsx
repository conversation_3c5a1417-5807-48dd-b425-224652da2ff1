import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { gameAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const GamePage = () => {
  const { roomCode } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [game, setGame] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentWord, setCurrentWord] = useState('');
  const [gameState, setGameState] = useState('waiting'); // waiting, playing, finished

  useEffect(() => {
    if (roomCode) {
      loadGame();
    } else {
      // No room code, redirect to home
      navigate('/');
    }
  }, [roomCode]);

  const loadGame = async () => {
    try {
      setLoading(true);
      const response = await gameAPI.getGameByRoomCode(roomCode);
      if (response.success && response.game) {
        setGame(response.game);
        setGameState(response.game.status.toLowerCase());
      } else {
        toast.error('Oyun bulunamadı');
        navigate('/');
      }
    } catch (error) {
      console.error('Game load error:', error);
      toast.error('Oyun yüklenemedi');
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const handleWordSubmit = async (e) => {
    e.preventDefault();
    if (!currentWord.trim()) return;

    try {
      await gameAPI.submitWord(game.id, currentWord.trim());
      setCurrentWord('');
      toast.success('Kelime gönderildi!');
    } catch (error) {
      console.error('Word submit error:', error);
      toast.error('Kelime gönderilemedi');
    }
  };

  const handleLeaveGame = async () => {
    try {
      await gameAPI.leaveGame(game.id);
      navigate('/');
      toast.success('Oyundan ayrıldınız');
    } catch (error) {
      console.error('Leave game error:', error);
      toast.error('Oyundan ayrılırken hata oluştu');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Oyun yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!game) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Oyun Bulunamadı</h1>
          <button
            onClick={() => navigate('/')}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700"
          >
            Ana Sayfaya Dön
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 p-4"
    >
      <div className="max-w-4xl mx-auto">
        {/* Game Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Kelime Zinciri</h1>
              <p className="text-gray-600">Oda Kodu: {roomCode}</p>
            </div>
            <button
              onClick={handleLeaveGame}
              className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600"
            >
              Oyundan Ayrıl
            </button>
          </div>
        </div>

        {/* Game Status */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Oyun Durumu</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Durum</p>
              <p className="text-lg font-semibold capitalize">{gameState}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Oyuncular</p>
              <p className="text-lg font-semibold">{game.currentPlayerCount || 0}/{game.maxPlayers || 4}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Zorluk</p>
              <p className="text-lg font-semibold">{game.difficulty || 'Orta'}</p>
            </div>
          </div>
        </div>

        {/* Players */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Oyuncular</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {game.players?.map((player, index) => (
              <div key={player.user.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {player.user.username?.charAt(0).toUpperCase()}
                </div>
                <div>
                  <p className="font-semibold">{player.user.username}</p>
                  <p className="text-sm text-gray-600">Skor: {player.score || 0}</p>
                </div>
                {player.user.id === user?.id && (
                  <span className="ml-auto text-xs bg-primary-100 text-primary-800 px-2 py-1 rounded">
                    Sen
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Game Area */}
        {gameState === 'playing' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Kelime Gir</h2>
            <form onSubmit={handleWordSubmit} className="space-y-4">
              <div>
                <input
                  type="text"
                  value={currentWord}
                  onChange={(e) => setCurrentWord(e.target.value)}
                  placeholder="Kelimenizi girin..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  autoFocus
                />
              </div>
              <button
                type="submit"
                disabled={!currentWord.trim()}
                className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Kelime Gönder
              </button>
            </form>
          </div>
        )}

        {/* Waiting State */}
        {gameState === 'waiting' && (
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">Oyun Başlamayı Bekliyor</h2>
            <p className="text-gray-600 mb-4">
              Oyunun başlaması için daha fazla oyuncunun katılması bekleniyor.
            </p>
            <div className="animate-pulse">
              <div className="w-8 h-8 bg-primary-600 rounded-full mx-auto"></div>
            </div>
          </div>
        )}

        {/* Game Finished */}
        {gameState === 'finished' && (
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">Oyun Bitti!</h2>
            <p className="text-gray-600 mb-4">
              Tebrikler! Oyun tamamlandı.
            </p>
            <button
              onClick={() => navigate('/')}
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700"
            >
              Ana Sayfaya Dön
            </button>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default GamePage;
