# 🐘 PostgreSQL Kurulum Rehberi - <PERSON><PERSON><PERSON> Zinci<PERSON>, <PERSON><PERSON><PERSON> Zinciri projesinde PostgreSQL'i kurmanız ve yapılandırmanız için adım adım talimatlar içerir.

## 📋 İçindekiler
1. [PostgreSQL Kurulumu](#postgresql-kurulumu)
2. [Veritabanı Oluşturma](#veritabanı-oluşturma)
3. [<PERSON><PERSON>](#proje-konfigürasyonu)
4. [Migration Çalıştırma](#migration-çalıştırma)
5. [Docker ile Ku<PERSON>lum](#docker-ile-kurulum)
6. [Sorun Giderme](#sorun-giderme)

## 🚀 PostgreSQL Kurulumu

### Windows
1. [PostgreSQL resmi sitesinden](https://www.postgresql.org/download/windows/) indirin
2. Installer'ı çalıştırın
3. Kurulum sırasında:
   - Port: `5432` (varsay<PERSON>lan)
   - Superuser şifresi: `KelimeZinciri123!` (veya kendi şifrenizi)
   - Locale: `Turkish, Turkey` (Türkçe destek için)

### macOS (Homebrew)
```bash
# PostgreSQL'i yükleyin
brew install postgresql

# PostgreSQL servisini başlatın
brew services start postgresql

# PostgreSQL'e bağlanın
psql postgres
```

### Ubuntu/Debian
```bash
# Paket listesini güncelleyin
sudo apt update

# PostgreSQL'i yükleyin
sudo apt install postgresql postgresql-contrib

# PostgreSQL servisini başlatın
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 🗄️ Veritabanı Oluşturma

### 1. PostgreSQL'e Bağlanma
```bash
# Windows (Command Prompt)
psql -U postgres -h localhost

# Linux/macOS
sudo -u postgres psql
```

### 2. Veritabanı ve Kullanıcı Oluşturma
```sql
-- Veritabanını oluşturun
CREATE DATABASE "KelimeZinciriDB"
WITH ENCODING 'UTF8'
LC_COLLATE='tr_TR.UTF-8'
LC_CTYPE='tr_TR.UTF-8';

-- Kullanıcı oluşturun (eğer yoksa)
CREATE USER kelimiyo WITH PASSWORD '********';

-- Yetkileri verin
GRANT ALL PRIVILEGES ON DATABASE "KelimeZinciriDB" TO kelimiyo;

-- Gerekli uzantıları etkinleştirin
\c "KelimeZinciriDB"
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Çıkış
\q
```

## ⚙️ Proje Konfigürasyonu

### 1. Connection String Kontrolü
`appsettings.json` dosyasında connection string'in doğru olduğundan emin olun:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=KelimeZinciriDB;Username=kelimiyo;Password=********;Include Error Detail=true"
  }
}
```

### 2. NuGet Paketlerini Kontrol Etme
```bash
# Proje dizinine gidin
cd Backend/KelimeZinciri.API

# Paketleri geri yükleyin
dotnet restore

# Build yapın
dotnet build
```

## 🔄 Migration Çalıştırma

### 1. İlk Migration Oluşturma
```bash
# Migration oluşturun
dotnet ef migrations add InitialPostgreSQLMigration

# Veritabanını güncelleyin
dotnet ef database update
```

### 2. Migration Sorunları Çözme
Eğer migration hatası alırsanız:

```bash
# Mevcut migration'ları silin
dotnet ef migrations remove

# Veritabanını temizleyin
dotnet ef database drop

# Yeni migration oluşturun
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## 🐳 Docker ile Kurulum

### 1. Tek Container
```bash
# PostgreSQL container'ını başlatın
docker run --name kelime-postgres \
  -e POSTGRES_DB=KelimeZinciriDB \
  -e POSTGRES_USER=kelimiyo \
  -e POSTGRES_PASSWORD=******** \
  -p 5432:5432 \
  -d postgres:16-alpine
```

### 2. Docker Compose ile
```bash
# Proje kök dizininde
cd Docker

# Tüm servisleri başlatın
docker-compose up -d

# Sadece PostgreSQL'i başlatın
docker-compose up -d postgres
```

### 3. Container Durumunu Kontrol Etme
```bash
# Çalışan container'ları listeleyin
docker ps

# PostgreSQL loglarını görün
docker logs kelime-postgres

# Container'a bağlanın
docker exec -it kelime-postgres psql -U kelimiyo -d KelimeZinciriDB
```

## 🔧 Sorun Giderme

### Yaygın Hatalar ve Çözümleri

#### 1. "Connection refused" Hatası
```bash
# PostgreSQL servisinin çalışıp çalışmadığını kontrol edin
# Windows
net start postgresql-x64-14

# Linux/macOS
sudo systemctl status postgresql
```

#### 2. "Authentication failed" Hatası
- Şifrenin doğru olduğundan emin olun
- `pg_hba.conf` dosyasında authentication method'unu kontrol edin

#### 3. "Database does not exist" Hatası
```sql
-- PostgreSQL'e bağlanın ve veritabanını oluşturun
CREATE DATABASE "KelimeZinciriDB";
```

#### 4. Migration Hataları
```bash
# Migration'ları sıfırlayın
dotnet ef database drop --force
dotnet ef migrations remove
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### Performans Optimizasyonu

#### 1. PostgreSQL Konfigürasyonu
`postgresql.conf` dosyasında:
```conf
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# Connection settings
max_connections = 100

# Turkish locale
lc_messages = 'tr_TR.UTF-8'
lc_monetary = 'tr_TR.UTF-8'
lc_numeric = 'tr_TR.UTF-8'
lc_time = 'tr_TR.UTF-8'
```

#### 2. Index Optimizasyonu
Proje otomatik olarak gerekli index'leri oluşturur, ancak manuel olarak da ekleyebilirsiniz:

```sql
-- Kelime arama için
CREATE INDEX IF NOT EXISTS idx_words_text_gin ON words USING gin (text gin_trgm_ops);

-- Kullanıcı istatistikleri için
CREATE INDEX IF NOT EXISTS idx_statistics_total_score ON statistics (total_score DESC);
```

## ✅ Kurulum Doğrulama

### 1. Bağlantı Testi
```bash
# Proje dizininde
dotnet run

# Swagger UI'yi açın
# https://localhost:7001/swagger
```

### 2. Veritabanı Testi
```sql
-- PostgreSQL'e bağlanın
\c "KelimeZinciriDB"

-- Tabloları listeleyin
\dt

-- Örnek sorgu
SELECT COUNT(*) FROM users;
```

## 📞 Destek

Sorun yaşıyorsanız:
1. Bu rehberdeki adımları tekrar kontrol edin
2. PostgreSQL loglarını inceleyin
3. Proje README.md dosyasına bakın
4. GitHub Issues'da sorun bildirin

---

**Not:** Bu rehber PostgreSQL 14+ sürümleri için hazırlanmıştır. Daha eski sürümler için bazı komutlar farklı olabilir.
