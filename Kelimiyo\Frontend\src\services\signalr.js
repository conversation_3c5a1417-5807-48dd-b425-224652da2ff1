import { HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import toast from 'react-hot-toast';

class SignalRService {
  constructor() {
    this.connection = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.eventHandlers = new Map();
  }

  async connect(token) {
    if (this.connection && this.isConnected) {
      return;
    }

    try {
      this.connection = new HubConnectionBuilder()
        .withUrl(`${import.meta.env.VITE_API_URL || 'https://localhost:7001'}/gameHub`, {
          accessTokenFactory: () => token,
          withCredentials: false,
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: (retryContext) => {
            // Exponential backoff: 1s, 2s, 4s, 8s, 16s
            return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 16000);
          }
        })
        .configureLogging(LogLevel.Information)
        .build();

      // Set up connection event handlers
      this.setupConnectionEvents();

      // Set up game event handlers
      this.setupGameEvents();

      await this.connection.start();
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      console.log('SignalR connected successfully');
      toast.success('Bağlantı kuruldu');

    } catch (error) {
      console.error('SignalR connection failed:', error);
      this.isConnected = false;
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        toast.error(`Bağlantı hatası. ${delay / 1000} saniye sonra tekrar denenecek...`);
        
        setTimeout(() => {
          this.connect(token);
        }, delay);
      } else {
        toast.error('Bağlantı kurulamadı. Sayfayı yenileyin.');
      }
    }
  }

  setupConnectionEvents() {
    this.connection.onclose((error) => {
      this.isConnected = false;
      console.log('SignalR connection closed:', error);
      
      if (error) {
        toast.error('Bağlantı kesildi');
      }
    });

    this.connection.onreconnecting((error) => {
      this.isConnected = false;
      console.log('SignalR reconnecting:', error);
      toast.loading('Yeniden bağlanıyor...', { id: 'reconnecting' });
    });

    this.connection.onreconnected((connectionId) => {
      this.isConnected = true;
      console.log('SignalR reconnected:', connectionId);
      toast.success('Bağlantı yeniden kuruldu', { id: 'reconnecting' });
    });
  }

  setupGameEvents() {
    // Player events
    this.connection.on('PlayerJoined', (data) => {
      this.emit('playerJoined', data);
      toast.success(`${data.username} oyuna katıldı`);
    });

    this.connection.on('PlayerLeft', (data) => {
      this.emit('playerLeft', data);
      toast.info(`${data.username} oyundan ayrıldı`);
    });

    // Game state events
    this.connection.on('GameStarted', (data) => {
      this.emit('gameStarted', data);
      toast.success('Oyun başladı!');
    });

    this.connection.on('GameEnded', (data) => {
      this.emit('gameEnded', data);
      toast.success('Oyun bitti!');
    });

    this.connection.on('GameState', (data) => {
      this.emit('gameState', data);
    });

    // Word events
    this.connection.on('WordSubmitted', (data) => {
      this.emit('wordSubmitted', data);
      
      if (data.points > 0) {
        toast.success(`${data.username}: "${data.word}" (+${data.points} puan)`);
      }
    });

    this.connection.on('WordRejected', (data) => {
      this.emit('wordRejected', data);
      toast.error(`Kelime reddedildi: ${data.reason}`);
    });

    this.connection.on('WordValidation', (data) => {
      this.emit('wordValidation', data);
    });

    // Chat events
    this.connection.on('ChatMessage', (data) => {
      this.emit('chatMessage', data);
    });

    // Typing events
    this.connection.on('PlayerTyping', (data) => {
      this.emit('playerTyping', data);
    });

    // Error events
    this.connection.on('Error', (message) => {
      console.error('SignalR error:', message);
      toast.error(message);
    });
  }

  // Event emitter methods
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event).push(handler);
  }

  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      const handlers = this.eventHandlers.get(event);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error);
        }
      });
    }
  }

  // Game methods
  async joinGameRoom(roomCode) {
    if (!this.isConnected) {
      throw new Error('SignalR bağlantısı yok');
    }
    
    try {
      await this.connection.invoke('JoinGameRoom', roomCode);
    } catch (error) {
      console.error('Error joining game room:', error);
      throw error;
    }
  }

  async leaveGameRoom(roomCode) {
    if (!this.isConnected) {
      return;
    }
    
    try {
      await this.connection.invoke('LeaveGameRoom', roomCode);
    } catch (error) {
      console.error('Error leaving game room:', error);
    }
  }

  async startGame(gameId) {
    if (!this.isConnected) {
      throw new Error('SignalR bağlantısı yok');
    }
    
    try {
      await this.connection.invoke('StartGame', gameId);
    } catch (error) {
      console.error('Error starting game:', error);
      throw error;
    }
  }

  async submitWord(gameId, word) {
    if (!this.isConnected) {
      throw new Error('SignalR bağlantısı yok');
    }
    
    try {
      await this.connection.invoke('SubmitWord', gameId, word);
    } catch (error) {
      console.error('Error submitting word:', error);
      throw error;
    }
  }

  async sendChatMessage(gameId, message) {
    if (!this.isConnected) {
      throw new Error('SignalR bağlantısı yok');
    }
    
    try {
      await this.connection.invoke('SendChatMessage', gameId, message);
    } catch (error) {
      console.error('Error sending chat message:', error);
      throw error;
    }
  }

  async requestGameState(gameId) {
    if (!this.isConnected) {
      throw new Error('SignalR bağlantısı yok');
    }
    
    try {
      await this.connection.invoke('RequestGameState', gameId);
    } catch (error) {
      console.error('Error requesting game state:', error);
      throw error;
    }
  }

  async validateWord(word, requiredStartLetter = null) {
    if (!this.isConnected) {
      throw new Error('SignalR bağlantısı yok');
    }
    
    try {
      await this.connection.invoke('ValidateWord', word, requiredStartLetter);
    } catch (error) {
      console.error('Error validating word:', error);
      throw error;
    }
  }

  async playerTyping(gameId, isTyping) {
    if (!this.isConnected) {
      return;
    }
    
    try {
      await this.connection.invoke('PlayerTyping', gameId, isTyping);
    } catch (error) {
      console.error('Error sending typing status:', error);
    }
  }

  async disconnect() {
    if (this.connection) {
      try {
        await this.connection.stop();
      } catch (error) {
        console.error('Error disconnecting SignalR:', error);
      } finally {
        this.connection = null;
        this.isConnected = false;
        this.eventHandlers.clear();
      }
    }
  }

  getConnectionState() {
    if (!this.connection) {
      return 'Disconnected';
    }
    return this.connection.state;
  }

  isConnectionActive() {
    return this.isConnected && this.connection?.state === 'Connected';
  }
}

// Create singleton instance
const signalRService = new SignalRService();

export default signalRService;
