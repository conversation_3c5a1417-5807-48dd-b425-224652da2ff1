@echo off
echo ========================================
echo    Kelime Zinciri - Ku<PERSON><PERSON> Scripti
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Bu script yönetici olarak çalıştırılmalıdır.
    echo Lütfen "Yönetici olarak çalıştır" seçeneğ<PERSON> kullanın.
    pause
    exit /b 1
)

:: Set variables
set BACKEND_DIR=%~dp0Backend\KelimeZinciri.API
set FRONTEND_DIR=%~dp0Frontend
set DOCKER_DIR=%~dp0Docker

echo Kurulum başlatılıyor...
echo.

:: Check prerequisites
echo [1/8] Ön gereksinimler kontrol ediliyor...

:: Check .NET 8.0
dotnet --version >nul 2>&1
if %errorLevel% neq 0 (
    echo HATA: .NET 8.0 SDK bulunamadı.
    echo Lütfen https://dotnet.microsoft.com/download adresinden .NET 8.0 SDK'yı indirin.
    pause
    exit /b 1
)

:: Check Node.js
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo HATA: Node.js bulunamadı.
    echo Lütfen https://nodejs.org adresinden Node.js'i indirin.
    pause
    exit /b 1
)

echo ✓ .NET 8.0 SDK bulundu
echo ✓ Node.js bulundu
echo.

:: Ask user for setup type
echo Kurulum türünü seçin:
echo 1. Geliştirme ortamı (Development)
echo 2. Docker ile kurulum
echo 3. Production kurulumu
echo.
set /p SETUP_TYPE="Seçiminizi yapın (1-3): "

if "%SETUP_TYPE%"=="1" goto DEVELOPMENT_SETUP
if "%SETUP_TYPE%"=="2" goto DOCKER_SETUP
if "%SETUP_TYPE%"=="3" goto PRODUCTION_SETUP

echo Geçersiz seçim. Geliştirme ortamı kurulumu başlatılıyor...
goto DEVELOPMENT_SETUP

:DEVELOPMENT_SETUP
echo.
echo ========================================
echo     GELİŞTİRME ORTAMI KURULUMU
echo ========================================
echo.

:: Backend setup
echo [2/8] Backend kurulumu...
cd /d "%BACKEND_DIR%"
if %errorLevel% neq 0 (
    echo HATA: Backend dizini bulunamadı: %BACKEND_DIR%
    pause
    exit /b 1
)

echo NuGet paketleri yükleniyor...
dotnet restore
if %errorLevel% neq 0 (
    echo HATA: NuGet paketleri yüklenemedi.
    pause
    exit /b 1
)

echo ✓ Backend paketleri yüklendi
echo.

:: Database setup
echo [3/8] Veritabanı kurulumu...
echo PostgreSQL veritabanı kontrol ediliyor...
echo UYARI: PostgreSQL'in çalıştığından emin olun.
echo Varsayılan bağlantı: Host=localhost;Port=5432;Database=KelimeZinciriDB;Username=kelimiyo;Password=admin123
echo.
echo Entity Framework migration'ları uygulanıyor...
dotnet ef database update
if %errorLevel% neq 0 (
    echo UYARI: Veritabanı migration'ları uygulanamadı.
    echo Lütfen PostgreSQL'in çalıştığından ve bağlantı bilgilerinin doğru olduğundan emin olun.
    echo Devam etmek için bir tuşa basın...
    pause
)

echo ✓ Veritabanı hazırlandı
echo.

:: Frontend setup
echo [4/8] Frontend kurulumu...
cd /d "%FRONTEND_DIR%"
if %errorLevel% neq 0 (
    echo HATA: Frontend dizini bulunamadı: %FRONTEND_DIR%
    pause
    exit /b 1
)

echo NPM paketleri yükleniyor...
npm install
if %errorLevel% neq 0 (
    echo HATA: NPM paketleri yüklenemedi.
    pause
    exit /b 1
)

echo ✓ Frontend paketleri yüklendi
echo.

:: Create startup scripts
echo [5/8] Başlatma scriptleri oluşturuluyor...

:: Backend start script
echo @echo off > "%~dp0start-backend.bat"
echo echo Backend başlatılıyor... >> "%~dp0start-backend.bat"
echo cd /d "%BACKEND_DIR%" >> "%~dp0start-backend.bat"
echo dotnet run >> "%~dp0start-backend.bat"

:: Frontend start script
echo @echo off > "%~dp0start-frontend.bat"
echo echo Frontend başlatılıyor... >> "%~dp0start-frontend.bat"
echo cd /d "%FRONTEND_DIR%" >> "%~dp0start-frontend.bat"
echo npm run dev >> "%~dp0start-frontend.bat"

:: Combined start script
echo @echo off > "%~dp0start-all.bat"
echo echo Kelime Zinciri başlatılıyor... >> "%~dp0start-all.bat"
echo echo. >> "%~dp0start-all.bat"
echo echo Backend başlatılıyor... >> "%~dp0start-all.bat"
echo start "Kelime Zinciri Backend" cmd /k "cd /d \"%BACKEND_DIR%\" && dotnet run" >> "%~dp0start-all.bat"
echo timeout /t 5 /nobreak ^>nul >> "%~dp0start-all.bat"
echo echo Frontend başlatılıyor... >> "%~dp0start-all.bat"
echo start "Kelime Zinciri Frontend" cmd /k "cd /d \"%FRONTEND_DIR%\" && npm run dev" >> "%~dp0start-all.bat"
echo echo. >> "%~dp0start-all.bat"
echo echo Uygulamalar başlatıldı! >> "%~dp0start-all.bat"
echo echo Backend: https://localhost:7001 >> "%~dp0start-all.bat"
echo echo Frontend: http://localhost:3000 >> "%~dp0start-all.bat"
echo pause >> "%~dp0start-all.bat"

echo ✓ Başlatma scriptleri oluşturuldu
echo.

goto SETUP_COMPLETE

:DOCKER_SETUP
echo.
echo ========================================
echo        DOCKER İLE KURULUM
echo ========================================
echo.

:: Check Docker
docker --version >nul 2>&1
if %errorLevel% neq 0 (
    echo HATA: Docker bulunamadı.
    echo Lütfen https://www.docker.com/get-started adresinden Docker'ı indirin.
    pause
    exit /b 1
)

echo ✓ Docker bulundu
echo.

echo [2/8] Docker container'ları oluşturuluyor...
cd /d "%DOCKER_DIR%"
docker-compose up -d --build
if %errorLevel% neq 0 (
    echo HATA: Docker container'ları oluşturulamadı.
    pause
    exit /b 1
)

echo ✓ Docker container'ları oluşturuldu
echo.

:: Create Docker management scripts
echo [3/8] Docker yönetim scriptleri oluşturuluyor...

echo @echo off > "%~dp0docker-start.bat"
echo echo Docker container'ları başlatılıyor... >> "%~dp0docker-start.bat"
echo cd /d "%DOCKER_DIR%" >> "%~dp0docker-start.bat"
echo docker-compose up -d >> "%~dp0docker-start.bat"
echo echo Container'lar başlatıldı! >> "%~dp0docker-start.bat"
echo pause >> "%~dp0docker-start.bat"

echo @echo off > "%~dp0docker-stop.bat"
echo echo Docker container'ları durduruluyor... >> "%~dp0docker-stop.bat"
echo cd /d "%DOCKER_DIR%" >> "%~dp0docker-stop.bat"
echo docker-compose down >> "%~dp0docker-stop.bat"
echo echo Container'lar durduruldu! >> "%~dp0docker-stop.bat"
echo pause >> "%~dp0docker-stop.bat"

echo @echo off > "%~dp0docker-logs.bat"
echo echo Docker logları gösteriliyor... >> "%~dp0docker-logs.bat"
echo cd /d "%DOCKER_DIR%" >> "%~dp0docker-logs.bat"
echo docker-compose logs -f >> "%~dp0docker-logs.bat"

echo ✓ Docker yönetim scriptleri oluşturuldu
echo.

goto SETUP_COMPLETE

:PRODUCTION_SETUP
echo.
echo ========================================
echo       PRODUCTION KURULUMU
echo ========================================
echo.

echo [2/8] Production build oluşturuluyor...

:: Backend production build
echo Backend production build...
cd /d "%BACKEND_DIR%"
dotnet publish -c Release -o "%~dp0publish\backend"
if %errorLevel% neq 0 (
    echo HATA: Backend production build başarısız.
    pause
    exit /b 1
)

:: Frontend production build
echo Frontend production build...
cd /d "%FRONTEND_DIR%"
npm run build
if %errorLevel% neq 0 (
    echo HATA: Frontend production build başarısız.
    pause
    exit /b 1
)

echo ✓ Production build'ler oluşturuldu
echo.

goto SETUP_COMPLETE

:SETUP_COMPLETE
echo [6/8] Konfigürasyon dosyaları kontrol ediliyor...

:: Check if appsettings.json exists and has correct structure
if not exist "%BACKEND_DIR%\appsettings.json" (
    echo UYARI: appsettings.json bulunamadı.
    echo Varsayılan konfigürasyon kullanılacak.
)

echo ✓ Konfigürasyon dosyaları kontrol edildi
echo.

echo [7/8] Son kontroller yapılıyor...

:: Create desktop shortcuts (optional)
set /p CREATE_SHORTCUTS="Masaüstü kısayolları oluşturulsun mu? (y/n): "
if /i "%CREATE_SHORTCUTS%"=="y" (
    echo Masaüstü kısayolları oluşturuluyor...

    :: This would require additional VBScript or PowerShell to create actual shortcuts
    echo Kısayol oluşturma özelliği gelecek sürümde eklenecek.
)

echo ✓ Son kontroller tamamlandı
echo.

echo [8/8] Kurulum tamamlanıyor...
echo.

echo ========================================
echo         KURULUM TAMAMLANDI!
echo ========================================
echo.

if "%SETUP_TYPE%"=="1" (
    echo Geliştirme ortamı hazır!
    echo.
    echo Başlatma seçenekleri:
    echo - start-all.bat: Hem backend hem frontend'i başlatır
    echo - start-backend.bat: Sadece backend'i başlatır
    echo - start-frontend.bat: Sadece frontend'i başlatır
    echo.
    echo URL'ler:
    echo - Backend API: https://localhost:7001
    echo - Frontend: http://localhost:3000
    echo - Swagger UI: https://localhost:7001/swagger
    echo.
    echo Şimdi başlatmak ister misiniz? (y/n):
    set /p START_NOW=""
    if /i "%START_NOW%"=="y" (
        start "" "%~dp0start-all.bat"
    )
) else if "%SETUP_TYPE%"=="2" (
    echo Docker kurulumu tamamlandı!
    echo.
    echo Yönetim scriptleri:
    echo - docker-start.bat: Container'ları başlatır
    echo - docker-stop.bat: Container'ları durdurur
    echo - docker-logs.bat: Logları gösterir
    echo.
    echo URL'ler:
    echo - Frontend: http://localhost:3000
    echo - Backend API: http://localhost:5000
    echo.
    echo Container'lar şu anda çalışıyor!
) else (
    echo Production build'ler oluşturuldu!
    echo.
    echo Build dosyaları:
    echo - Backend: %~dp0publish\backend\
    echo - Frontend: %FRONTEND_DIR%\dist\
    echo.
    echo Bu dosyaları production sunucunuza deploy edebilirsiniz.
)

echo.
echo Daha fazla bilgi için README.md dosyasını okuyun.
echo.
echo İyi oyunlar! 🎯
echo.
pause

cd /d "%~dp0"
exit /b 0
