import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Plus,
  Users,
  Trophy,
  Clock,
  Target,
  Zap,
  Play,
  Search
} from 'lucide-react';
import toast from 'react-hot-toast';

import { useAuth } from '../contexts/AuthContext';
import { gameAPI, statisticsAPI, leaderboardAPI } from '../services/api';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const HomePage = () => {
  const [activeGames, setActiveGames] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [joinRoomCode, setJoinRoomCode] = useState('');
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [stats, setStats] = useState({
    totalGames: 0,
    activeUsers: 0,
    weeklyGames: 0,
    bestScore: 0
  });

  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    await Promise.all([
      loadActiveGames(),
      loadUserStats(),
      loadGlobalStats()
    ]);
  };

  const loadActiveGames = async () => {
    try {
      const response = await gameAPI.getActiveGames();
      if (response.success) {
        setActiveGames(response.games);
      }
    } catch (error) {
      console.error('Error loading active games:', error);
      toast.error('Aktif oyunlar yüklenirken hata oluştu');
    }
  };

  const loadUserStats = async () => {
    try {
      const response = await statisticsAPI.getMyStats();
      if (response.success) {
        const userStats = response.data;
        setStats(prev => ({
          ...prev,
          totalGames: userStats.gamesPlayed || 0,
          bestScore: userStats.bestScore || 0
        }));
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const loadGlobalStats = async () => {
    try {
      // Get weekly leaderboard to calculate weekly games
      const weeklyResponse = await leaderboardAPI.getWeeklyLeaderboard(100);
      if (weeklyResponse.success) {
        const weeklyGames = weeklyResponse.data.reduce((sum, entry) => sum + entry.gamesPlayed, 0);
        setStats(prev => ({
          ...prev,
          weeklyGames: weeklyGames
        }));
      }

      // Get global leaderboard to estimate active users
      const globalResponse = await leaderboardAPI.getGlobalLeaderboard(100);
      if (globalResponse.success) {
        setStats(prev => ({
          ...prev,
          activeUsers: globalResponse.data.length
        }));
      }
    } catch (error) {
      console.error('Error loading global stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateGame = async () => {
    try {
      const gameData = {
        difficulty: 2, // Medium (1=Easy, 2=Medium, 3=Hard)
        maxPlayers: 4,
        timeLimit: 30,
        maxRounds: 10
      };

      const response = await gameAPI.createRoom(gameData);
      if (response.success) {
        const roomCode = response.game.roomCode;
        navigate(`/game/${roomCode}`);
        toast.success('Oyun odası oluşturuldu!');
      } else {
        toast.error('Oyun oluşturulamadı');
      }
    } catch (error) {
      console.error('Create game error:', error);
      toast.error('Oyun oluşturulurken hata oluştu');
    }
  };

  const handleJoinGame = async (roomCode = null) => {
    const code = roomCode || joinRoomCode.toUpperCase();

    if (!code) {
      toast.error('Oda kodu giriniz');
      return;
    }

    try {
      const response = await gameAPI.joinRoom(code);
      if (response.success) {
        navigate(`/game/${code}`);
        toast.success('Oyuna katıldınız!');
      } else {
        toast.error('Oyun odası bulunamadı');
      }
    } catch (error) {
      toast.error('Oyuna katılırken hata oluştu');
    }

    setShowJoinModal(false);
    setJoinRoomCode('');
  };

  const quickStats = [
    { label: 'Toplam Oyun', value: stats.totalGames.toLocaleString(), icon: Target, color: 'text-blue-600' },
    { label: 'Aktif Oyuncu', value: stats.activeUsers.toLocaleString(), icon: Users, color: 'text-green-600' },
    { label: 'Bu Hafta', value: stats.weeklyGames.toLocaleString(), icon: Clock, color: 'text-purple-600' },
    { label: 'Rekor Skor', value: stats.bestScore.toLocaleString(), icon: Trophy, color: 'text-yellow-600' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Hoş geldin, <span className="gradient-text">{user?.username}</span>! 🎯
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Arkadaşlarınla kelime zinciri oyna, kelime dağarcığını geliştir ve liderlik tablosunda yüksel!
          </p>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12"
        >
          {quickStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.label}
                whileHover={{ scale: 1.05 }}
                className="card text-center"
              >
                <Icon className={`w-8 h-8 ${stat.color} mx-auto mb-2`} />
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-600">{stat.label}</p>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid md:grid-cols-2 gap-6 mb-12"
        >
          {/* Create Game */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleCreateGame}
            className="card-hover p-8 text-left bg-gradient-to-br from-primary-500 to-primary-600 text-white"
          >
            <div className="flex items-center justify-between mb-4">
              <Plus className="w-12 h-12" />
              <Zap className="w-8 h-8 opacity-70" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Yeni Oyun Oluştur</h3>
            <p className="text-primary-100">
              Kendi oyun odanı oluştur ve arkadaşlarını davet et
            </p>
          </motion.button>

          {/* Join Game */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowJoinModal(true)}
            className="card-hover p-8 text-left bg-gradient-to-br from-secondary-500 to-secondary-600 text-white"
          >
            <div className="flex items-center justify-between mb-4">
              <Search className="w-12 h-12" />
              <Users className="w-8 h-8 opacity-70" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Oyuna Katıl</h3>
            <p className="text-secondary-100">
              Oda kodu ile mevcut bir oyuna katıl
            </p>
          </motion.button>
        </motion.div>

        {/* Active Games */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mb-12"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Aktif Oyunlar</h2>
            <button
              onClick={loadData}
              className="btn-ghost text-sm"
            >
              Yenile
            </button>
          </div>

          {isLoading ? (
            <div className="text-center py-12">
              <LoadingSpinner size="lg" text="Oyunlar yükleniyor..." />
            </div>
          ) : activeGames.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {activeGames.map((game) => (
                <motion.div
                  key={game.id}
                  whileHover={{ scale: 1.02 }}
                  className="card-hover"
                >
                  <div className="flex items-center justify-between mb-3">
                    <span className="badge badge-primary">
                      {game.difficulty}
                    </span>
                    <span className="text-sm text-gray-500">
                      {game.currentPlayerCount}/{game.maxPlayers}
                    </span>
                  </div>

                  <h3 className="font-semibold text-gray-900 mb-2">
                    Oda: {game.roomCode}
                  </h3>

                  <p className="text-sm text-gray-600 mb-3">
                    Oluşturan: {game.createdByUser.username}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Users size={14} />
                      <span>{game.currentPlayerCount} oyuncu</span>
                    </div>

                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleJoinGame(game.roomCode)}
                      className="btn-primary btn-sm"
                    >
                      <Play size={14} />
                      Katıl
                    </motion.button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Aktif oyun bulunamadı
              </h3>
              <p className="text-gray-600 mb-6">
                İlk oyunu sen oluştur ve arkadaşlarını davet et!
              </p>
              <button
                onClick={handleCreateGame}
                className="btn-primary"
              >
                Yeni Oyun Oluştur
              </button>
            </div>
          )}
        </motion.div>

        {/* Join Room Modal */}
        {showJoinModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white rounded-xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Oyuna Katıl
              </h3>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Oda Kodu
                </label>
                <input
                  type="text"
                  value={joinRoomCode}
                  onChange={(e) => setJoinRoomCode(e.target.value.toUpperCase())}
                  placeholder="ABC123"
                  className="input text-center text-lg font-mono tracking-wider"
                  maxLength={6}
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowJoinModal(false)}
                  className="btn-secondary flex-1"
                >
                  İptal
                </button>
                <button
                  onClick={() => handleJoinGame()}
                  disabled={!joinRoomCode}
                  className="btn-primary flex-1"
                >
                  Katıl
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
