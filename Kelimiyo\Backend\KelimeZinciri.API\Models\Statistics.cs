using System.ComponentModel.DataAnnotations;

namespace KelimeZinciri.API.Models
{
    public class Statistics
    {
        public int Id { get; set; }
        public int UserId { get; set; }

        // Game Statistics
        public int GamesPlayed { get; set; } = 0;
        public int GamesWon { get; set; } = 0;
        public int GamesLost { get; set; } = 0;

        // Score Statistics
        public int TotalScore { get; set; } = 0;
        public int BestScore { get; set; } = 0;

        // Word Statistics
        public int TotalWordsUsed { get; set; } = 0;
        public double AverageWordLength { get; set; } = 0;
        public string LongestWord { get; set; } = string.Empty;

        // Time Statistics
        public int FastestGameTime { get; set; } = 0; // in seconds

        // Streak Statistics
        public int CurrentStreak { get; set; } = 0;
        public int BestStreak { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual User User { get; set; } = null!;
    }


}
